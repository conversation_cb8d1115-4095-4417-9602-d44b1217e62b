<?php
// Quick template system diagnostic for new thread
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'aomprouddyogiki_db';

$mysqli = new mysqli($host, $username, $password, $database);
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

echo "=== TEMPLATE SYSTEM DIAGNOSTIC ===\n\n";

// Current active template
$result = $mysqli->query("SELECT id, name, slug FROM cms_templates WHERE is_default = 1");
$active = $result->fetch_assoc();
echo "Active Template: {$active['name']} ({$active['slug']})\n";

// Template assets for active template
echo "\nActive Template Assets:\n";
$cssResult = $mysqli->query("SELECT file_path FROM cms_template_assets WHERE template_id = {$active['id']} AND asset_type = 'css' ORDER BY load_order");
while ($css = $cssResult->fetch_assoc()) {
    $exists = file_exists(__DIR__ . '/public/' . $css['file_path']) ? '✅' : '❌';
    echo "  CSS: {$exists} {$css['file_path']}\n";
}

$jsResult = $mysqli->query("SELECT file_path FROM cms_template_assets WHERE template_id = {$active['id']} AND asset_type = 'js' ORDER BY load_order");
while ($js = $jsResult->fetch_assoc()) {
    $exists = file_exists(__DIR__ . '/public/' . $js['file_path']) ? '✅' : '❌';
    echo "  JS:  {$exists} {$js['file_path']}\n";
}

// Template settings
$result = $mysqli->query("SELECT setting_value FROM cms_template_settings WHERE setting_key = 'active_template'");
$setting = $result->fetch_assoc();
echo "\nTemplate Setting: {$setting['setting_value']}\n";
echo "Settings Match: " . ($setting['setting_value'] === $active['slug'] ? '✅ YES' : '❌ NO') . "\n";

// All templates status
echo "\nAll Templates:\n";
$result = $mysqli->query("SELECT name, slug, is_default, status FROM cms_templates ORDER BY is_default DESC, name");
while ($row = $result->fetch_assoc()) {
    $status = $row['is_default'] ? '🟢 ACTIVE' : '⚪ INACTIVE';
    echo "  {$status} {$row['name']} ({$row['slug']}) - {$row['status']}\n";
}

echo "\n=== DIAGNOSTIC COMPLETE ===\n";
$mysqli->close();
?>
