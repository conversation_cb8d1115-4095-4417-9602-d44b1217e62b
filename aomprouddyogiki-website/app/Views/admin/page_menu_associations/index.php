<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('head') ?>
<style>
    .association-card {
        transition: all 0.3s ease;
    }
    .association-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .status-badge {
        font-size: 0.75rem;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    .action-buttons .btn {
        margin: 2px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Page-Menu Associations</h1>
            <p class="text-muted">Manage relationships between pages and menu items</p>
        </div>
        <div>
            <a href="<?= base_url('admin/pages') ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Pages
            </a>
            <a href="<?= base_url('admin/menus') ?>" class="btn btn-outline-primary">
                <i class="fas fa-bars me-1"></i> Manage Menus
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h4><?= $stats['total_pages'] ?></h4>
                    <small>Total Pages</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="fas fa-link fa-2x mb-2"></i>
                    <h4><?= $stats['linked_pages'] ?></h4>
                    <small>Linked Pages</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="fas fa-unlink fa-2x mb-2"></i>
                    <h4><?= $stats['unlinked_pages'] ?></h4>
                    <small>Unlinked Pages</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="fas fa-bars fa-2x mb-2"></i>
                    <h4><?= $stats['unlinked_menus'] ?></h4>
                    <small>Available Menus</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">Bulk Actions</h5>
            <div class="action-buttons">
                <a href="<?= base_url('admin/page-menu-associations/auto-link') ?>" 
                   class="btn btn-primary" 
                   onclick="return confirm('This will automatically link pages to menus based on title and URL matching. Continue?')">
                    <i class="fas fa-magic me-1"></i> Auto-Link Pages & Menus
                </a>
                <a href="<?= base_url('admin/page-menu-associations/generate-all-routes') ?>" 
                   class="btn btn-success"
                   onclick="return confirm('This will generate dynamic routes for all published pages. Continue?')">
                    <i class="fas fa-route me-1"></i> Generate All Routes
                </a>
                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#bulkCreateMenuModal">
                    <i class="fas fa-plus-circle me-1"></i> Bulk Create Menus
                </button>
            </div>
        </div>
    </div>

    <!-- Pages and Associations -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Page-Menu Associations</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Page</th>
                            <th>URL</th>
                            <th>Menu Association</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pages as $page): ?>
                        <tr>
                            <td>
                                <strong><?= esc($page['title']) ?></strong>
                                <br><small class="text-muted">ID: <?= $page['id'] ?></small>
                            </td>
                            <td>
                                <code><?= esc($page['full_slug'] ?: $page['slug']) ?></code>
                                <br><small class="text-muted">/{<?= esc($page['full_slug'] ?: $page['slug']) ?>}</small>
                            </td>
                            <td>
                                <?php if (!empty($page['menu_title'])): ?>
                                    <span class="badge bg-success status-badge">
                                        <i class="fas fa-link me-1"></i>
                                        <?= esc($page['menu_title']) ?>
                                    </span>
                                    <br><small class="text-muted">Menu ID: <?= $page['menu_id'] ?></small>
                                <?php else: ?>
                                    <span class="badge bg-secondary status-badge">
                                        <i class="fas fa-unlink me-1"></i>
                                        No Menu
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($page['status'] === 'published'): ?>
                                    <span class="badge bg-success">Published</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Draft</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <?php if (empty($page['menu_title'])): ?>
                                        <button type="button" class="btn btn-outline-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#linkMenuModal"
                                                data-page-id="<?= $page['id'] ?>"
                                                data-page-title="<?= esc($page['title']) ?>">
                                            <i class="fas fa-link"></i> Link
                                        </button>
                                        <button type="button" class="btn btn-outline-success" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#createMenuModal"
                                                data-page-id="<?= $page['id'] ?>"
                                                data-page-title="<?= esc($page['title']) ?>">
                                            <i class="fas fa-plus"></i> Create
                                        </button>
                                    <?php else: ?>
                                        <a href="<?= base_url('admin/page-menu-associations/unlink/' . $page['id']) ?>" 
                                           class="btn btn-outline-danger"
                                           onclick="return confirm('Unlink this page from its menu?')">
                                            <i class="fas fa-unlink"></i> Unlink
                                        </a>
                                    <?php endif; ?>
                                    <a href="<?= base_url('admin/pages/edit/' . $page['id']) ?>" 
                                       class="btn btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Link to Existing Menu Modal -->
<div class="modal fade" id="linkMenuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="<?= base_url('admin/page-menu-associations/link-page-to-menu') ?>">
                <div class="modal-header">
                    <h5 class="modal-title">Link Page to Menu</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="page_id" id="link-page-id">
                    <p>Link page "<span id="link-page-title"></span>" to an existing menu item:</p>
                    
                    <div class="mb-3">
                        <label for="menu_id" class="form-label">Select Menu Item</label>
                        <select class="form-select" name="menu_id" required>
                            <option value="">-- Select Menu Item --</option>
                            <?php foreach ($unlinkedMenus as $menu): ?>
                                <option value="<?= $menu['id'] ?>">
                                    <?= esc($menu['title']) ?> (<?= esc($menu['url']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Link to Menu</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create New Menu Modal -->
<div class="modal fade" id="createMenuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="<?= base_url('admin/page-menu-associations/create-menu-for-page') ?>">
                <div class="modal-header">
                    <h5 class="modal-title">Create Menu for Page</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="page_id" id="create-page-id">
                    <p>Create a new menu item for page "<span id="create-page-title"></span>":</p>
                    
                    <div class="mb-3">
                        <label for="menu_location" class="form-label">Menu Location</label>
                        <select class="form-select" name="menu_location">
                            <option value="primary">Primary Navigation</option>
                            <option value="footer">Footer Menu</option>
                            <option value="sidebar">Sidebar Menu</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="menu_parent_id" class="form-label">Parent Menu (Optional)</label>
                        <select class="form-select" name="menu_parent_id">
                            <option value="">-- Top Level Menu --</option>
                            <!-- Will be populated based on location -->
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Create Menu</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    // Handle link menu modal
    document.getElementById('linkMenuModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const pageId = button.getAttribute('data-page-id');
        const pageTitle = button.getAttribute('data-page-title');
        
        document.getElementById('link-page-id').value = pageId;
        document.getElementById('link-page-title').textContent = pageTitle;
    });

    // Handle create menu modal
    document.getElementById('createMenuModal').addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        const pageId = button.getAttribute('data-page-id');
        const pageTitle = button.getAttribute('data-page-title');
        
        document.getElementById('create-page-id').value = pageId;
        document.getElementById('create-page-title').textContent = pageTitle;
    });
</script>
<?= $this->endSection() ?>
