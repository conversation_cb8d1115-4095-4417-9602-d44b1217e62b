<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('head') ?>
<style>
.install-option {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
}

.install-option:hover {
    border-color: #007bff;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.install-option.active {
    border-color: #007bff;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

.install-option .icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.popular-template {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.popular-template:hover {
    border-color: #007bff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.popular-template.selected {
    border-color: #007bff;
    background: #f8f9ff;
}

.template-preview-small {
    width: 80px;
    height: 60px;
    background: #f8f9fa;
    border-radius: 5px;
    overflow: hidden;
}

.template-preview-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
}

.upload-area.dragover {
    border-color: #007bff;
    background: #f8f9ff;
}

.form-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.form-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.progress-step {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.step-number.active {
    background: #007bff;
    color: white;
}

.step-number.completed {
    background: #28a745;
    color: white;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Install New Template</h1>
            <p class="text-muted">Add new templates to your website</p>
        </div>
        <a href="<?= base_url('admin/templates') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Templates
        </a>
    </div>

    <!-- Progress Steps -->
    <div class="progress-step">
        <div class="step-number active" id="step1">1</div>
        <div>
            <h5 class="mb-0">Choose Installation Method</h5>
            <small class="text-muted">Select how you want to install the template</small>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Installation Method Selection -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="install-option" onclick="selectInstallMethod('templatemo')">
                <div class="icon">
                    <i class="fas fa-globe"></i>
                </div>
                <h4>From TemplateMo.com</h4>
                <p class="text-muted">Download and install templates directly from TemplateMo.com</p>
                <div class="badge bg-success">Recommended</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="install-option" onclick="selectInstallMethod('upload')">
                <div class="icon">
                    <i class="fas fa-upload"></i>
                </div>
                <h4>Upload Template File</h4>
                <p class="text-muted">Upload a ZIP file containing your template</p>
                <div class="badge bg-info">Custom Templates</div>
            </div>
        </div>
    </div>

    <!-- Installation Forms -->
    <form method="POST" action="<?= base_url('admin/templates/install') ?>" enctype="multipart/form-data" id="installForm">
        <input type="hidden" name="install_type" id="installType">

        <!-- TemplateMo Installation -->
        <div class="form-section" id="templatemoSection">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-globe"></i> Install from TemplateMo.com</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="templatemoUrl" class="form-label">TemplateMo URL</label>
                                <input type="url" class="form-control" id="templatemoUrl" name="templatemo_url" 
                                       placeholder="https://templatemo.com/tm-562-space-dynamic">
                                <div class="form-text">
                                    Enter the full URL of the template from TemplateMo.com (e.g., https://templatemo.com/tm-562-space-dynamic)
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <button type="button" class="btn btn-outline-primary" onclick="searchTemplates()">
                                    <i class="fas fa-search"></i> Search Templates
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="showPopularTemplates()">
                                    <i class="fas fa-star"></i> Popular Templates
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6><i class="fas fa-info-circle"></i> How to find templates:</h6>
                                    <ol class="small">
                                        <li>Visit <a href="https://templatemo.com" target="_blank">TemplateMo.com</a></li>
                                        <li>Browse or search for templates</li>
                                        <li>Copy the template URL</li>
                                        <li>Paste it in the field above</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Popular Templates -->
                    <div id="popularTemplates" style="display: none;">
                        <h6>Popular Templates</h6>
                        <div class="row">
                            <?php if (!empty($popularTemplates)): ?>
                                <?php foreach (array_slice($popularTemplates, 0, 6) as $template): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="popular-template" onclick="selectPopularTemplate('<?= esc($template['source_url']) ?>')">
                                            <div class="d-flex align-items-center">
                                                <div class="template-preview-small me-3">
                                                    <?php if (!empty($template['preview_image'])): ?>
                                                        <img src="<?= esc($template['preview_image']) ?>" alt="<?= esc($template['name']) ?>">
                                                    <?php else: ?>
                                                        <div class="d-flex align-items-center justify-content-center h-100">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <h6 class="mb-1"><?= esc($template['name']) ?></h6>
                                                    <small class="text-muted"><?= esc($template['templatemo_id']) ?></small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        Popular templates will be loaded automatically. Please check your internet connection.
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Search Results -->
                    <div id="searchResults" style="display: none;">
                        <h6>Search Results</h6>
                        <div id="searchResultsContainer"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Installation -->
        <div class="form-section" id="uploadSection">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-upload"></i> Upload Template File</h5>
                </div>
                <div class="card-body">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>Drag & Drop Template File</h5>
                        <p class="text-muted">Or click to browse for files</p>
                        <input type="file" class="form-control" id="templateFile" name="template_file" 
                               accept=".zip" style="display: none;">
                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('templateFile').click()">
                            <i class="fas fa-folder-open"></i> Browse Files
                        </button>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Supported formats: ZIP files only. Maximum size: 50MB.
                        </small>
                    </div>

                    <div id="fileInfo" style="display: none;" class="mt-3">
                        <div class="alert alert-info">
                            <i class="fas fa-file-archive"></i>
                            <strong>Selected file:</strong> <span id="fileName"></span>
                            <br>
                            <strong>Size:</strong> <span id="fileSize"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Installation Button -->
        <div class="text-center mt-4">
            <button type="submit" class="btn btn-success btn-lg" id="installButton" disabled>
                <i class="fas fa-download"></i> Install Template
            </button>
        </div>
    </form>
</div>

<script>
let selectedMethod = null;

function selectInstallMethod(method) {
    selectedMethod = method;
    
    // Update UI
    document.querySelectorAll('.install-option').forEach(option => {
        option.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    
    // Show appropriate form section
    document.querySelectorAll('.form-section').forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById(method + 'Section').classList.add('active');
    
    // Set install type
    document.getElementById('installType').value = method;
    
    // Update install button
    updateInstallButton();
}

function updateInstallButton() {
    const button = document.getElementById('installButton');
    let canInstall = false;
    
    if (selectedMethod === 'templatemo') {
        const url = document.getElementById('templatemoUrl').value;
        canInstall = url && url.includes('templatemo.com');
    } else if (selectedMethod === 'upload') {
        const file = document.getElementById('templateFile').files[0];
        canInstall = file && file.type === 'application/zip';
    }
    
    button.disabled = !canInstall;
}

function showPopularTemplates() {
    const container = document.getElementById('popularTemplates');
    container.style.display = container.style.display === 'none' ? 'block' : 'none';
    document.getElementById('searchResults').style.display = 'none';
}

function selectPopularTemplate(url) {
    document.getElementById('templatemoUrl').value = url;
    document.querySelectorAll('.popular-template').forEach(template => {
        template.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
    updateInstallButton();
}

function searchTemplates() {
    const query = prompt('Enter search terms:');
    if (!query) return;
    
    // Show loading
    const container = document.getElementById('searchResultsContainer');
    container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';
    document.getElementById('searchResults').style.display = 'block';
    document.getElementById('popularTemplates').style.display = 'none';
    
    // Perform search (this would be an AJAX call in a real implementation)
    fetch(`<?= base_url('admin/templates/search') ?>?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.templates && data.templates.length > 0) {
                let html = '<div class="row">';
                data.templates.forEach(template => {
                    html += `
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="popular-template" onclick="selectPopularTemplate('${template.source_url}')">
                                <div class="d-flex align-items-center">
                                    <div class="template-preview-small me-3">
                                        ${template.preview_image ? 
                                            `<img src="${template.preview_image}" alt="${template.name}">` :
                                            '<div class="d-flex align-items-center justify-content-center h-100"><i class="fas fa-image text-muted"></i></div>'
                                        }
                                    </div>
                                    <div>
                                        <h6 class="mb-1">${template.name}</h6>
                                        <small class="text-muted">${template.templatemo_id}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                html += '</div>';
                container.innerHTML = html;
            } else {
                container.innerHTML = '<div class="alert alert-info">No templates found for your search.</div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div class="alert alert-danger">Search failed. Please try again.</div>';
        });
}

// File upload handling
document.getElementById('templateFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = (file.size / 1024 / 1024).toFixed(2) + ' MB';
        document.getElementById('fileInfo').style.display = 'block';
        updateInstallButton();
    }
});

// Drag and drop handling
const uploadArea = document.getElementById('uploadArea');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        document.getElementById('templateFile').files = files;
        document.getElementById('templateFile').dispatchEvent(new Event('change'));
    }
});

// URL input handling
document.getElementById('templatemoUrl').addEventListener('input', updateInstallButton);
</script>
<?= $this->endSection() ?>
