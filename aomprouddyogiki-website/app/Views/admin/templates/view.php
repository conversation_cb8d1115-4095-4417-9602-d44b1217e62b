<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('head') ?>
<style>
.template-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.template-preview-large {
    width: 100%;
    height: 300px;
    background: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.template-preview-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-preview-large .placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-size: 4rem;
}

.info-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.section-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-left: 4px solid #007bff;
}

.section-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.asset-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.asset-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-weight: bold;
}

.asset-css { background: #007bff; }
.asset-js { background: #ffc107; color: #000; }
.asset-image { background: #28a745; }
.asset-font { background: #6f42c1; }
.asset-other { background: #6c757d; }

.validation-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
}

.validation-icon {
    width: 20px;
    margin-right: 0.5rem;
}

.config-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 0.5rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.status-active { background: #d4edda; color: #155724; }
.status-inactive { background: #f8d7da; color: #721c24; }
.status-installing { background: #fff3cd; color: #856404; }
.status-error { background: #f5c6cb; color: #721c24; }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Template Header -->
    <div class="template-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center mb-2">
                    <h1 class="h3 mb-0 me-3"><?= esc($template['name']) ?></h1>
                    <span class="status-badge status-<?= $template['status'] ?>">
                        <?= ucfirst($template['status']) ?>
                    </span>
                    <?php if ($template['is_default']): ?>
                        <span class="badge bg-success ms-2">
                            <i class="fas fa-check"></i> Active Template
                        </span>
                    <?php endif; ?>
                </div>
                <p class="mb-2"><?= esc($template['description']) ?></p>
                <div class="d-flex align-items-center">
                    <span class="me-3"><i class="fas fa-user"></i> <?= esc($template['author'] ?? 'Unknown') ?></span>
                    <span class="me-3"><i class="fas fa-tag"></i> v<?= esc($template['version']) ?></span>
                    <?php if ($template['templatemo_id']): ?>
                        <span class="me-3"><i class="fas fa-globe"></i> <?= esc($template['templatemo_id']) ?></span>
                    <?php endif; ?>
                    <span><i class="fas fa-calendar"></i> <?= date('M j, Y', strtotime($template['installation_date'])) ?></span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group" role="group">
                    <?php if (!$template['is_default'] && $template['status'] === 'active'): ?>
                        <button type="button" class="btn btn-light" onclick="switchTemplate(<?= $template['id'] ?>, '<?= esc($template['name']) ?>')">
                            <i class="fas fa-exchange-alt"></i> Activate
                        </button>
                    <?php endif; ?>
                    <a href="<?= base_url('admin/templates/sections/' . $template['id']) ?>" class="btn btn-light">
                        <i class="fas fa-puzzle-piece"></i> Sections
                    </a>
                    <a href="<?= base_url('admin/templates/assets/' . $template['id']) ?>" class="btn btn-light">
                        <i class="fas fa-file-code"></i> Assets
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Back Button -->
    <div class="mb-3">
        <a href="<?= base_url('admin/templates') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Templates
        </a>
    </div>

    <div class="row">
        <!-- Template Preview -->
        <div class="col-lg-8">
            <div class="info-card">
                <h5><i class="fas fa-eye"></i> Template Preview</h5>
                <div class="template-preview-large">
                    <?php if (!empty($template['preview_image'])): ?>
                        <img src="<?= base_url($template['preview_image']) ?>" alt="<?= esc($template['name']) ?>">
                    <?php else: ?>
                        <div class="placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($template['source_url'])): ?>
                    <div class="mt-3">
                        <a href="<?= esc($template['source_url']) ?>" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> View Original
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Template Sections -->
            <div class="info-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-puzzle-piece"></i> Template Sections</h5>
                    <span class="badge bg-primary"><?= count($template['sections']) ?> sections</span>
                </div>
                
                <?php if (!empty($template['sections'])): ?>
                    <?php foreach ($template['sections'] as $section): ?>
                        <div class="section-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?= esc($section['section_name']) ?></h6>
                                    <small class="text-muted">
                                        <?php if ($section['html_selector']): ?>
                                            <i class="fas fa-code"></i> <?= esc($section['html_selector']) ?>
                                        <?php endif; ?>
                                        <?php if ($section['css_classes']): ?>
                                            <i class="fas fa-palette ms-2"></i> <?= esc($section['css_classes']) ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <div>
                                    <span class="section-type-badge bg-<?= $section['section_type'] === 'header' ? 'primary' : ($section['section_type'] === 'footer' ? 'secondary' : 'info') ?>">
                                        <?= esc($section['section_type']) ?>
                                    </span>
                                    <?php if ($section['is_editable']): ?>
                                        <span class="badge bg-success ms-1">Editable</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-puzzle-piece fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No sections found. Template may need to be parsed.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Template Assets -->
            <div class="info-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-file-code"></i> Template Assets</h5>
                    <span class="badge bg-primary"><?= count($template['assets']) ?> assets</span>
                </div>
                
                <?php if (!empty($template['assets'])): ?>
                    <?php 
                    $assetsByType = [];
                    foreach ($template['assets'] as $asset) {
                        $assetsByType[$asset['asset_type']][] = $asset;
                    }
                    ?>
                    
                    <?php foreach ($assetsByType as $type => $assets): ?>
                        <h6 class="text-uppercase text-muted mb-2"><?= ucfirst($type) ?> Files (<?= count($assets) ?>)</h6>
                        <?php foreach ($assets as $asset): ?>
                            <div class="asset-item">
                                <div class="asset-icon asset-<?= $asset['asset_type'] ?>">
                                    <?php
                                    $icons = [
                                        'css' => 'fab fa-css3-alt',
                                        'js' => 'fab fa-js-square',
                                        'image' => 'fas fa-image',
                                        'font' => 'fas fa-font',
                                        'other' => 'fas fa-file'
                                    ];
                                    ?>
                                    <i class="<?= $icons[$asset['asset_type']] ?? $icons['other'] ?>"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1"><?= esc($asset['file_name']) ?></h6>
                                    <small class="text-muted">
                                        <?= esc($asset['file_path']) ?>
                                        <?php if ($asset['file_size']): ?>
                                            • <?= number_format($asset['file_size'] / 1024, 1) ?>KB
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <div>
                                    <?php if ($asset['is_critical']): ?>
                                        <span class="badge bg-warning">Critical</span>
                                    <?php endif; ?>
                                    <span class="badge bg-light text-dark">Order: <?= $asset['load_order'] ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <hr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-file-code fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No assets found. Template may need to be parsed.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Template Information -->
        <div class="col-lg-4">
            <!-- Template Validation -->
            <div class="info-card">
                <h5><i class="fas fa-check-circle"></i> Template Validation</h5>
                
                <div class="validation-item">
                    <div class="validation-icon">
                        <i class="fas fa-<?= $validation['valid'] ? 'check text-success' : 'times text-danger' ?>"></i>
                    </div>
                    <span>Overall Structure: <?= $validation['valid'] ? 'Valid' : 'Invalid' ?></span>
                </div>
                
                <div class="validation-item">
                    <div class="validation-icon">
                        <i class="fas fa-puzzle-piece text-info"></i>
                    </div>
                    <span>Total Sections: <?= $validation['total_sections'] ?></span>
                </div>
                
                <div class="validation-item">
                    <div class="validation-icon">
                        <i class="fas fa-edit text-success"></i>
                    </div>
                    <span>Editable Sections: <?= $validation['editable_sections'] ?></span>
                </div>
                
                <?php if (!empty($validation['missing_sections'])): ?>
                    <div class="alert alert-warning mt-2">
                        <small><strong>Missing sections:</strong> <?= implode(', ', $validation['missing_sections']) ?></small>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Template Configuration -->
            <?php
            $configData = $template['config_data'] ?? [];
            if (is_string($configData)) {
                $configData = json_decode($configData, true) ?? [];
            }
            ?>
            <?php if (!empty($configData)): ?>
                <div class="info-card">
                    <h5><i class="fas fa-cog"></i> Configuration</h5>

                    <?php foreach ($configData as $key => $value): ?>
                        <div class="config-item">
                            <strong><?= ucwords(str_replace('_', ' ', $key)) ?>:</strong>
                            <div class="mt-1">
                                <?php if (is_array($value)): ?>
                                    <?php if (empty($value)): ?>
                                        <span class="text-muted">None</span>
                                    <?php else: ?>
                                        <?php foreach ($value as $item): ?>
                                            <span class="badge bg-light text-dark me-1"><?= esc($item) ?></span>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                <?php elseif (is_bool($value)): ?>
                                    <span class="badge bg-<?= $value ? 'success' : 'secondary' ?>">
                                        <?= $value ? 'Yes' : 'No' ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted"><?= esc($value) ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- Template Actions -->
            <div class="info-card">
                <h5><i class="fas fa-tools"></i> Actions</h5>
                
                <div class="d-grid gap-2">
                    <?php if (!$template['is_default'] && in_array($template['status'], ['active', 'inactive'])): ?>
                        <button type="button" class="btn btn-success" onclick="switchTemplate(<?= $template['id'] ?>, '<?= esc($template['name']) ?>')">
                            <i class="fas fa-exchange-alt"></i> Activate Template
                        </button>
                    <?php endif; ?>
                    
                    <a href="<?= base_url('admin/templates/sections/' . $template['id']) ?>" class="btn btn-outline-primary">
                        <i class="fas fa-puzzle-piece"></i> Manage Sections
                    </a>
                    
                    <a href="<?= base_url('admin/templates/assets/' . $template['id']) ?>" class="btn btn-outline-info">
                        <i class="fas fa-file-code"></i> Manage Assets
                    </a>
                    
                    <?php if (!$template['is_default']): ?>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(<?= $template['id'] ?>, '<?= esc($template['name']) ?>')">
                            <i class="fas fa-trash"></i> Delete Template
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Switch Template Modal -->
<div class="modal fade" id="switchTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Switch Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?= base_url('admin/templates/switch') ?>">
                <div class="modal-body">
                    <p>Are you sure you want to switch to <strong id="templateName"></strong>?</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="createBackup" name="create_backup" value="1" checked>
                        <label class="form-check-label" for="createBackup">
                            Create backup of current template
                        </label>
                    </div>
                    <input type="hidden" id="templateId" name="template_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Switch Template</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Template Modal -->
<div class="modal fade" id="deleteTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteTemplateName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone. All template files and data will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="deleteTemplateLink" class="btn btn-danger">Delete Template</a>
            </div>
        </div>
    </div>
</div>

<script>
function switchTemplate(templateId, templateName) {
    document.getElementById('templateId').value = templateId;
    document.getElementById('templateName').textContent = templateName;
    new bootstrap.Modal(document.getElementById('switchTemplateModal')).show();
}

function deleteTemplate(templateId, templateName) {
    document.getElementById('deleteTemplateName').textContent = templateName;
    document.getElementById('deleteTemplateLink').href = '<?= base_url('admin/templates/delete/') ?>' + templateId;
    new bootstrap.Modal(document.getElementById('deleteTemplateModal')).show();
}
</script>
<?= $this->endSection() ?>
