<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('head') ?>
<style>
.template-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.template-card.active {
    border-color: #007bff;
    background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

.template-preview {
    height: 200px;
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-preview .placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6c757d;
    font-size: 3rem;
}

.template-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active { background: #d4edda; color: #155724; }
.status-inactive { background: #f8d7da; color: #721c24; }
.status-installing { background: #fff3cd; color: #856404; }
.status-error { background: #f5c6cb; color: #721c24; }

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Template Gallery</h1>
            <p class="text-muted">Manage and switch between website templates</p>
        </div>
        <div>
            <a href="<?= base_url('admin/templates/install') ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Install New Template
            </a>
            <a href="<?= base_url('admin/templates/settings') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-cog"></i> Settings
            </a>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Card -->
    <div class="stats-card">
        <h4><i class="fas fa-chart-bar"></i> Template Engine Statistics</h4>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number"><?= $stats['templates']['total'] ?? 0 ?></span>
                <span class="stat-label">Total Templates</span>
            </div>
            <div class="stat-item">
                <span class="stat-number"><?= $stats['templates']['active'] ?? 0 ?></span>
                <span class="stat-label">Active Templates</span>
            </div>
            <div class="stat-item">
                <span class="stat-number"><?= isset($stats['active_template']) ? $stats['active_template']['name'] : 'None' ?></span>
                <span class="stat-label">Current Template</span>
            </div>
            <div class="stat-item">
                <span class="stat-number"><?= number_format(($stats['themes_directory_size'] ?? 0) / 1024 / 1024, 1) ?>MB</span>
                <span class="stat-label">Storage Used</span>
            </div>
        </div>
    </div>

    <!-- Templates Grid -->
    <div class="row">
        <?php if (empty($templates)): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-palette fa-3x text-muted mb-3"></i>
                    <h4>No Templates Found</h4>
                    <p class="text-muted">Get started by installing your first template</p>
                    <a href="<?= base_url('admin/templates/install') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Install Template
                    </a>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($templates as $template): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card template-card h-100 <?= $template['is_default'] ? 'active' : '' ?>">
                        <!-- Template Preview -->
                        <div class="template-preview">
                            <?php if (!empty($template['preview_image'])): ?>
                                <img src="<?= base_url($template['preview_image']) ?>" alt="<?= esc($template['name']) ?>" loading="lazy">
                            <?php else: ?>
                                <div class="placeholder">
                                    <i class="fas fa-image"></i>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Status Badge -->
                            <span class="template-status status-<?= $template['status'] ?>">
                                <?= ucfirst($template['status']) ?>
                            </span>
                            
                            <!-- Active Badge -->
                            <?php if ($template['is_default']): ?>
                                <span class="badge bg-success position-absolute" style="top: 10px; left: 10px;">
                                    <i class="fas fa-check"></i> Active
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="card-body">
                            <h5 class="card-title"><?= esc($template['name']) ?></h5>
                            <p class="card-text text-muted small">
                                <?= esc(substr($template['description'] ?? 'No description available', 0, 100)) ?>
                                <?= strlen($template['description'] ?? '') > 100 ? '...' : '' ?>
                            </p>
                            
                            <!-- Template Info -->
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <small class="text-muted">Version</small><br>
                                    <strong><?= esc($template['version']) ?></strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">Author</small><br>
                                    <strong><?= esc($template['author'] ?? 'Unknown') ?></strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">ID</small><br>
                                    <strong><?= esc($template['templatemo_id'] ?? 'Custom') ?></strong>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer bg-transparent">
                            <div class="btn-group w-100" role="group">
                                <a href="<?= base_url('admin/templates/view/' . $template['id']) ?>" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                
                                <?php if (!$template['is_default'] && in_array($template['status'], ['active', 'inactive'])): ?>
                                    <button type="button" class="btn btn-success btn-sm"
                                            onclick="switchTemplate(<?= $template['id'] ?>, '<?= esc($template['name']) ?>')">
                                        <i class="fas fa-exchange-alt"></i> Activate
                                    </button>
                                <?php endif; ?>
                                
                                <a href="<?= base_url('admin/templates/sections/' . $template['id']) ?>" 
                                   class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-puzzle-piece"></i> Sections
                                </a>
                                
                                <?php if (!$template['is_default']): ?>
                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                            onclick="deleteTemplate(<?= $template['id'] ?>, '<?= esc($template['name']) ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Switch Template Modal -->
<div class="modal fade" id="switchTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Switch Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?= base_url('admin/templates/switch') ?>">
                <div class="modal-body">
                    <p>Are you sure you want to switch to <strong id="templateName"></strong>?</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="createBackup" name="create_backup" value="1" checked>
                        <label class="form-check-label" for="createBackup">
                            Create backup of current template
                        </label>
                    </div>
                    <input type="hidden" id="templateId" name="template_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Switch Template</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Template Modal -->
<div class="modal fade" id="deleteTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteTemplateName"></strong>?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone. All template files and data will be permanently deleted.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <a href="#" id="deleteTemplateLink" class="btn btn-danger">Delete Template</a>
            </div>
        </div>
    </div>
</div>

<script>
function switchTemplate(templateId, templateName) {
    document.getElementById('templateId').value = templateId;
    document.getElementById('templateName').textContent = templateName;
    new bootstrap.Modal(document.getElementById('switchTemplateModal')).show();
}

function deleteTemplate(templateId, templateName) {
    document.getElementById('deleteTemplateName').textContent = templateName;
    document.getElementById('deleteTemplateLink').href = '<?= base_url('admin/templates/delete/') ?>' + templateId;
    new bootstrap.Modal(document.getElementById('deleteTemplateModal')).show();
}
</script>
<?= $this->endSection() ?>
