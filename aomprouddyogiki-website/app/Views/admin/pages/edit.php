<?= $this->extend('admin/layouts/admin') ?>

<?= $this->section('head') ?>
<!-- Quill.js Editor -->
<link href="https://cdn.quilljs.com/1.3.7/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.7/quill.min.js"></script>
<!-- Ace Editor for HTML Source Editor (Open Source) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/ace.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/mode-html.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/theme-monokai.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.32.6/ext-language_tools.min.js"></script>
<!-- GrapesJS Visual Page Builder (Open Source) -->
<link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
<script src="https://unpkg.com/grapesjs"></script>
<script src="https://unpkg.com/grapesjs-preset-webpage"></script>
<script src="https://unpkg.com/grapesjs-blocks-basic"></script>
<script src="https://unpkg.com/grapesjs-plugin-forms"></script>
<script src="https://unpkg.com/grapesjs-component-countdown"></script>
<script src="https://unpkg.com/grapesjs-plugin-export"></script>
<script src="https://unpkg.com/grapesjs-tabs"></script>
<script src="https://unpkg.com/grapesjs-custom-code"></script>
<script src="https://unpkg.com/grapesjs-parser-postcss"></script>
<script src="https://unpkg.com/grapesjs-tooltip"></script>
<script src="https://unpkg.com/grapesjs-tui-image-editor"></script>
<script src="https://unpkg.com/grapesjs-typed"></script>
<script src="https://unpkg.com/grapesjs-style-bg"></script>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-edit me-2"></i>
        Edit Page: <?= esc($page['title']) ?>
    </h2>
    <div>
        <a href="<?= base_url('admin/pages/preview/' . $page['id']) ?>" 
           class="btn btn-outline-info me-2" target="_blank">
            <i class="fas fa-eye me-2"></i>
            Preview
        </a>
        <a href="<?= base_url('admin/pages') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Pages
        </a>
    </div>
</div>

<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger">
        <h6>Please fix the following errors:</h6>
        <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<form action="<?= base_url('admin/pages/edit/' . $page['id']) ?>" method="POST">
    <?= csrf_field() ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Page Content</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">Page Title *</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?= old('title', $page['title']) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="slug" class="form-label">Page Slug *</label>
                        <input type="text" class="form-control" id="slug" name="slug"
                               value="<?= old('slug', $page['slug']) ?>" required>
                        <div class="form-text">URL-friendly version of the title.</div>
                    </div>

                    <div class="mb-3">
                        <label for="parent_id" class="form-label">Parent Page (Optional)</label>
                        <select class="form-select" id="parent_id" name="parent_id">
                            <option value="">-- No Parent (Top Level) --</option>
                            <?php if (isset($parentPages)): ?>
                                <?php foreach ($parentPages as $parent): ?>
                                    <?php if ($parent['id'] != $page['id']): // Prevent self-parenting ?>
                                        <option value="<?= $parent['id'] ?>" <?= old('parent_id', $page['parent_id']) == $parent['id'] ? 'selected' : '' ?>>
                                            <?= esc($parent['title']) ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <div class="form-text">Select a parent page to create hierarchical URLs (e.g., /parent/child)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="excerpt" class="form-label">Page Excerpt</label>
                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3"><?= old('excerpt', $page['excerpt']) ?></textarea>
                        <div class="form-text">Brief description of the page content.</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label for="content" class="form-label mb-0">Page Content</label>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" id="visual-btn">
                                    <i class="fas fa-paint-brush me-1"></i> Visual
                                </button>
                                <button type="button" class="btn btn-outline-primary active" id="wysiwyg-btn">
                                    <i class="fas fa-eye me-1"></i> WYSIWYG
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="html-btn">
                                    <i class="fas fa-code me-1"></i> HTML
                                </button>
                                <button type="button" class="btn btn-outline-success" id="image-btn">
                                    <i class="fas fa-image me-1"></i> Images
                                </button>
                            </div>
                        </div>
                        <!-- Visual Editor (GrapesJS) -->
                        <div id="visual-editor" style="height: 600px; display: none;"></div>
                        <!-- WYSIWYG Editor (Quill.js) -->
                        <div id="content-editor" style="height: 400px;"><?= old('content', $page['content']) ?></div>
                        <!-- HTML Source Editor (Ace) -->
                        <div id="html-editor" style="height: 400px; display: none;"></div>
                        <!-- Hidden textarea for form submission -->
                        <textarea id="content" name="content" style="display: none;"><?= old('content', $page['content']) ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Hero Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Hero Section</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_title" class="form-label">Hero Title</label>
                                <input type="text" class="form-control" id="hero_title" name="hero_title" 
                                       value="<?= old('hero_title', $page['hero_title']) ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_image" class="form-label">Hero Image</label>
                                <input type="text" class="form-control" id="hero_image" name="hero_image" 
                                       value="<?= old('hero_image', $page['hero_image']) ?>" placeholder="images/hero-image.jpg">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="hero_subtitle" class="form-label">Hero Subtitle</label>
                        <textarea class="form-control" id="hero_subtitle" name="hero_subtitle" rows="2"><?= old('hero_subtitle', $page['hero_subtitle']) ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_cta_text" class="form-label">CTA Button Text</label>
                                <input type="text" class="form-control" id="hero_cta_text" name="hero_cta_text" 
                                       value="<?= old('hero_cta_text', $page['hero_cta_text']) ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hero_cta_link" class="form-label">CTA Button Link</label>
                                <input type="text" class="form-control" id="hero_cta_link" name="hero_cta_link" 
                                       value="<?= old('hero_cta_link', $page['hero_cta_link']) ?>">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Content Blocks -->
            <?php if (!empty($contentBlocks)): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Content Blocks</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($contentBlocks as $block): ?>
                                    <tr>
                                        <td><span class="badge bg-info"><?= esc(ucfirst($block['block_type'])) ?></span></td>
                                        <td><?= esc($block['title'] ?: 'Untitled') ?></td>
                                        <td><span class="badge bg-<?= $block['status'] == 'active' ? 'success' : 'secondary' ?>"><?= esc(ucfirst($block['status'])) ?></span></td>
                                        <td>
                                            <a href="<?= base_url('admin/content-blocks/edit/' . $block['id']) ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <a href="<?= base_url('admin/content-blocks/create?page_id=' . $page['id']) ?>" 
                       class="btn btn-sm btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        Add Content Block
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="col-md-4">
            <!-- Page Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Page Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status *</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="draft" <?= old('status', $page['status']) == 'draft' ? 'selected' : '' ?>>Draft</option>
                            <option value="published" <?= old('status', $page['status']) == 'published' ? 'selected' : '' ?>>Published</option>
                            <option value="archived" <?= old('status', $page['status']) == 'archived' ? 'selected' : '' ?>>Archived</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template" class="form-label">Template *</label>
                        <select class="form-select" id="template" name="template" required>
                            <option value="default" <?= old('template', $page['template']) == 'default' ? 'selected' : '' ?>>Default</option>
                            <option value="service" <?= old('template', $page['template']) == 'service' ? 'selected' : '' ?>>Service</option>
                            <option value="product" <?= old('template', $page['template']) == 'product' ? 'selected' : '' ?>>Product</option>
                            <option value="about" <?= old('template', $page['template']) == 'about' ? 'selected' : '' ?>>About</option>
                            <option value="contact" <?= old('template', $page['template']) == 'contact' ? 'selected' : '' ?>>Contact</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">Sort Order</label>
                        <input type="number" class="form-control" id="sort_order" name="sort_order" 
                               value="<?= old('sort_order', $page['sort_order']) ?>" min="0">
                        <div class="form-text">Higher numbers appear first.</div>
                    </div>
                </div>
            </div>
            
            <!-- SEO Settings -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">SEO Settings</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label">Meta Title</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title" 
                               value="<?= old('meta_title', $page['meta_title']) ?>" maxlength="255">
                        <div class="form-text">Leave blank to use page title.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_description" class="form-label">Meta Description</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" 
                                  rows="3" maxlength="500"><?= old('meta_description', $page['meta_description']) ?></textarea>
                        <div class="form-text">Recommended: 150-160 characters.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">Meta Keywords</label>
                        <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" 
                               value="<?= old('meta_keywords', $page['meta_keywords']) ?>">
                        <div class="form-text">Comma-separated keywords.</div>
                    </div>
                </div>
            </div>
            
            <!-- Current Menu Associations -->
            <?php if (!empty($associatedMenus)): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Current Menu Associations</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($associatedMenus as $menu): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong><?= esc($menu['title']) ?></strong>
                                <br><small class="text-muted"><?= esc($menu['menu_location']) ?> menu</small>
                            </div>
                            <a href="<?= base_url('admin/page-menu-associations/unlink/' . $page['id']) ?>"
                               class="btn btn-sm btn-outline-danger"
                               onclick="return confirm('Unlink this page from the menu?')">
                                <i class="fas fa-unlink"></i>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Menu Association -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">Menu Association</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Menu Action</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="menu_action" id="menu_none" value="none" checked>
                            <label class="form-check-label" for="menu_none">
                                No changes to menu association
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="menu_action" id="menu_link_existing" value="link_existing">
                            <label class="form-check-label" for="menu_link_existing">
                                Link to existing menu item
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="menu_action" id="menu_create_new" value="create_new">
                            <label class="form-check-label" for="menu_create_new">
                                Create new menu item
                            </label>
                        </div>
                    </div>

                    <!-- Existing Menu Selection -->
                    <div class="mb-3" id="existing_menu_section" style="display: none;">
                        <label for="existing_menu_id" class="form-label">Select Menu Item</label>
                        <select class="form-select" id="existing_menu_id" name="existing_menu_id">
                            <option value="">-- Select Menu Item --</option>
                            <?php if (isset($existingMenus)): ?>
                                <?php foreach ($existingMenus as $menu): ?>
                                    <option value="<?= $menu['id'] ?>">
                                        <?= esc($menu['title']) ?> (<?= esc($menu['url']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <!-- New Menu Creation -->
                    <div id="new_menu_section" style="display: none;">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="create_new_menu" name="create_new_menu" value="1">
                            <label class="form-check-label" for="create_new_menu">
                                Create menu item for this page
                            </label>
                        </div>

                        <div class="mb-3">
                            <label for="menu_location" class="form-label">Menu Location</label>
                            <select class="form-select" id="menu_location" name="menu_location">
                                <option value="primary">Primary Navigation</option>
                                <option value="footer">Footer Menu</option>
                                <option value="sidebar">Sidebar Menu</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="menu_parent_id" class="form-label">Menu Parent (Optional)</label>
                            <select class="form-select" id="menu_parent_id" name="menu_parent_id">
                                <option value="">-- Top Level Menu --</option>
                                <!-- Will be populated via JavaScript based on location -->
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card mt-4">
                <div class="card-body">
                    <button type="submit" class="btn btn-primary w-100 mb-2">
                        <i class="fas fa-save me-2"></i>
                        Update Page
                    </button>
                    <a href="<?= base_url('admin/pages') ?>" class="btn btn-outline-secondary w-100 mb-2">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </a>
                    <a href="<?= base_url('admin/page-menu-associations') ?>" class="btn btn-outline-info w-100">
                        <i class="fas fa-link me-2"></i>
                        Manage All Associations
                    </a>
                </div>
            </div>
            
            <!-- Page Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">Page Information</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <strong>Created:</strong> <?= date('M j, Y g:i A', strtotime($page['created_at'])) ?><br>
                        <strong>Updated:</strong> <?= date('M j, Y g:i A', strtotime($page['updated_at'])) ?><br>
                        <strong>ID:</strong> <?= $page['id'] ?>
                    </small>
                </div>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    let currentEditor = 'wysiwyg';
    let quill, htmlEditor, visualEditor;
    let isUpdating = false; // Prevent infinite sync loops

    // Initialize Quill.js
    quill = new Quill('#content-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                ['link', 'image', 'video'],
                ['blockquote', 'code-block'],
                ['clean']
            ]
        },
        placeholder: 'Enter page content here...'
    });

    // Initialize Ace HTML Editor
    htmlEditor = ace.edit('html-editor');
    htmlEditor.setTheme('ace/theme/monokai');
    htmlEditor.session.setMode('ace/mode/html');
    htmlEditor.setOptions({
        enableBasicAutocompletion: true,
        enableSnippets: true,
        enableLiveAutocompletion: true,
        fontSize: 14,
        showPrintMargin: false,
        wrap: true
    });
    htmlEditor.setValue(document.getElementById('content').value || '', -1);

    // Sync Quill content with hidden textarea
    quill.on('text-change', function() {
        if (currentEditor === 'wysiwyg') {
            const content = quill.root.innerHTML;
            document.getElementById('content').value = content;
            htmlEditor.setValue(content, -1);
        }
    });

    // Sync Ace HTML editor content with hidden textarea
    htmlEditor.session.on('change', function() {
        if (currentEditor === 'html') {
            const content = htmlEditor.getValue();
            document.getElementById('content').value = content;
            quill.root.innerHTML = content;
        }
    });

    // Initialize GrapesJS Visual Editor
    visualEditor = grapesjs.init({
        container: '#visual-editor',
        height: '600px',
        width: 'auto',
        storageManager: false,
        plugins: [
            'gjs-blocks-basic',
            'grapesjs-plugin-forms',
            'grapesjs-component-countdown',
            'grapesjs-plugin-export',
            'grapesjs-tabs',
            'grapesjs-custom-code',
            'grapesjs-tooltip',
            'grapesjs-typed',
            'grapesjs-style-bg'
        ],
        pluginsOpts: {
            'gjs-blocks-basic': { flexGrid: true },
            'grapesjs-plugin-forms': {
                blocks: ['form', 'input', 'textarea', 'select', 'button', 'label', 'checkbox', 'radio']
            }
        },
        canvas: {
            styles: [
                'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css'
            ]
        }
    });

    // Sync Visual Editor changes
    visualEditor.on('component:update', function() {
        if (currentEditor === 'visual' && !isUpdating) {
            isUpdating = true;
            const htmlContent = visualEditor.getHtml();
            const cssContent = visualEditor.getCss();
            const fullContent = cssContent ? `<style>${cssContent}</style>${htmlContent}` : htmlContent;

            document.getElementById('content').value = fullContent;
            htmlEditor.setValue(fullContent, -1);
            quill.root.innerHTML = htmlContent;
            isUpdating = false;
        }
    });

    // Update existing sync functions to include visual editor
    quill.on('text-change', function() {
        if (currentEditor === 'wysiwyg' && !isUpdating) {
            isUpdating = true;
            const content = quill.root.innerHTML;
            document.getElementById('content').value = content;
            htmlEditor.setValue(content, -1);
            visualEditor.setComponents(content);
            isUpdating = false;
        }
    });

    htmlEditor.session.on('change', function() {
        if (currentEditor === 'html' && !isUpdating) {
            isUpdating = true;
            const content = htmlEditor.getValue();
            document.getElementById('content').value = content;
            quill.root.innerHTML = content;
            visualEditor.setComponents(content);
            isUpdating = false;
        }
    });

    // Initialize content if editing
    if (document.getElementById('content').value) {
        const content = document.getElementById('content').value;
        quill.root.innerHTML = content;
        htmlEditor.setValue(content, -1);
        visualEditor.setComponents(content);
    }

    // Editor toggle functionality
    document.getElementById('visual-btn').addEventListener('click', function() {
        if (currentEditor !== 'visual') {
            // Sync content to visual editor
            const currentContent = document.getElementById('content').value;
            visualEditor.setComponents(currentContent);

            // Toggle visibility
            document.getElementById('visual-editor').style.display = 'block';
            document.getElementById('content-editor').style.display = 'none';
            document.getElementById('html-editor').style.display = 'none';

            // Toggle button states
            document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            currentEditor = 'visual';
        }
    });

    document.getElementById('wysiwyg-btn').addEventListener('click', function() {
        if (currentEditor !== 'wysiwyg') {
            // Sync content to WYSIWYG editor
            const currentContent = document.getElementById('content').value;
            quill.root.innerHTML = currentContent;

            // Toggle visibility
            document.getElementById('visual-editor').style.display = 'none';
            document.getElementById('content-editor').style.display = 'block';
            document.getElementById('html-editor').style.display = 'none';

            // Toggle button states
            document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            currentEditor = 'wysiwyg';
        }
    });

    document.getElementById('html-btn').addEventListener('click', function() {
        if (currentEditor !== 'html') {
            // Sync content to HTML editor
            const currentContent = document.getElementById('content').value;
            htmlEditor.setValue(currentContent, -1);

            // Toggle visibility
            document.getElementById('visual-editor').style.display = 'none';
            document.getElementById('content-editor').style.display = 'none';
            document.getElementById('html-editor').style.display = 'block';
            htmlEditor.resize(); // Refresh Ace editor when shown

            // Toggle button states
            document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            currentEditor = 'html';
        }
    });

    // Image browser functionality
    document.getElementById('image-btn').addEventListener('click', function() {
        openImageBrowser();
    });

    function openImageBrowser() {
        // Create modal for image browser
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'imageBrowserModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Select Image</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <input type="file" class="form-control" id="quickUpload" accept="image/*" multiple>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-primary" onclick="loadModalImages()">
                                    <i class="fas fa-refresh me-1"></i> Refresh
                                </button>
                                <a href="<?= base_url('admin/images/browser') ?>" target="_blank" class="btn btn-outline-secondary">
                                    <i class="fas fa-external-link-alt me-1"></i> Full Browser
                                </a>
                            </div>
                        </div>
                        <div id="modalImageGrid" class="row g-2" style="max-height: 400px; overflow-y: auto;">
                            <div class="col-12 text-center py-4">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-2">Loading images...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="insertImageBtn" disabled>Insert Image</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Load images
        loadModalImages();

        // Handle quick upload
        document.getElementById('quickUpload').addEventListener('change', function(e) {
            Array.from(e.target.files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    uploadImageQuick(file);
                }
            });
        });

        // Clean up modal when closed
        modal.addEventListener('hidden.bs.modal', function() {
            modal.remove();
        });
    }

    function loadModalImages() {
        fetch('<?= base_url('admin/images/browse') ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayModalImages(data.images);
                } else {
                    document.getElementById('modalImageGrid').innerHTML =
                        '<div class="col-12 text-center py-4"><p class="text-muted">Error loading images</p></div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('modalImageGrid').innerHTML =
                    '<div class="col-12 text-center py-4"><p class="text-muted">Error loading images</p></div>';
            });
    }

    function displayModalImages(images) {
        const grid = document.getElementById('modalImageGrid');

        if (images.length === 0) {
            grid.innerHTML = '<div class="col-12 text-center py-4"><p class="text-muted">No images uploaded yet</p></div>';
            return;
        }

        grid.innerHTML = images.map(image => `
            <div class="col-md-3 col-sm-4 col-6">
                <div class="card image-select-card" data-url="${image.url}" style="cursor: pointer;">
                    <img src="${image.thumbnail || image.url}" class="card-img-top" style="height: 120px; object-fit: cover;" alt="${image.filename}">
                    <div class="card-body p-2">
                        <small class="text-muted" title="${image.filename}">${image.filename.length > 20 ? image.filename.substring(0, 20) + '...' : image.filename}</small>
                    </div>
                </div>
            </div>
        `).join('');

        // Add click handlers
        document.querySelectorAll('.image-select-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove previous selection
                document.querySelectorAll('.image-select-card').forEach(c => c.classList.remove('border-primary'));
                // Add selection
                this.classList.add('border-primary');
                // Enable insert button
                document.getElementById('insertImageBtn').disabled = false;
                document.getElementById('insertImageBtn').onclick = () => insertSelectedImage(this.dataset.url);
            });
        });
    }

    function insertSelectedImage(imageUrl) {
        if (currentEditor === 'visual') {
            // Insert into Visual Editor
            const selected = visualEditor.getSelected();
            if (selected) {
                selected.append(`<img src="${imageUrl}" alt="Image" style="max-width: 100%; height: auto;">`);
            } else {
                visualEditor.addComponents(`<img src="${imageUrl}" alt="Image" style="max-width: 100%; height: auto;">`);
            }
        } else if (currentEditor === 'wysiwyg') {
            // Insert into Quill
            const range = quill.getSelection();
            quill.insertEmbed(range ? range.index : 0, 'image', imageUrl);
        } else {
            // Insert into HTML editor
            const imgTag = `<img src="${imageUrl}" alt="Image" style="max-width: 100%; height: auto;">`;
            htmlEditor.insert(imgTag);
        }

        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('imageBrowserModal')).hide();
    }

    function uploadImageQuick(file) {
        const formData = new FormData();
        formData.append('image', file);

        fetch('<?= base_url('admin/images/upload') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadModalImages(); // Reload images
            } else {
                alert('Upload failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Upload error');
        });
    }
    
    // Character counter for meta description
    const metaDesc = document.getElementById('meta_description');
    const counter = document.createElement('div');
    counter.className = 'form-text text-end';
    metaDesc.parentNode.appendChild(counter);
    
    function updateCounter() {
        const length = metaDesc.value.length;
        counter.textContent = `${length}/500 characters`;
        counter.className = length > 160 ? 'form-text text-end text-warning' : 'form-text text-end';
    }
    
    metaDesc.addEventListener('input', updateCounter);
    updateCounter();

    // Menu association form handling
    document.querySelectorAll('input[name="menu_action"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const existingSection = document.getElementById('existing_menu_section');
            const newSection = document.getElementById('new_menu_section');

            // Hide all sections first
            existingSection.style.display = 'none';
            newSection.style.display = 'none';

            // Show relevant section
            if (this.value === 'link_existing') {
                existingSection.style.display = 'block';
            } else if (this.value === 'create_new') {
                newSection.style.display = 'block';
            }
        });
    });

    // Auto-check create menu checkbox when create_new is selected
    document.getElementById('menu_create_new').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('create_new_menu').checked = true;
        }
    });

    // URL preview functionality
    function updateUrlPreview() {
        const parentSelect = document.getElementById('parent_id');
        const slugInput = document.getElementById('slug');
        const titleInput = document.getElementById('title');

        let slug = slugInput.value;
        if (!slug && titleInput.value) {
            slug = titleInput.value.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
        }

        let fullUrl = '/' + slug;

        if (parentSelect.value) {
            const parentOption = parentSelect.options[parentSelect.selectedIndex];
            const parentText = parentOption.text;
            const parentSlug = parentText.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            fullUrl = '/' + parentSlug + '/' + slug;
        }

        // Update URL preview if element exists
        const urlPreview = document.getElementById('url-preview');
        if (urlPreview) {
            urlPreview.textContent = fullUrl;
        }
    }

    // Add URL preview element if not exists
    const slugInput = document.getElementById('slug');
    if (slugInput && !document.getElementById('url-preview')) {
        const urlPreviewDiv = document.createElement('div');
        urlPreviewDiv.className = 'form-text';
        urlPreviewDiv.innerHTML = '<strong>URL Preview:</strong> <span id="url-preview">/</span>';
        slugInput.parentNode.appendChild(urlPreviewDiv);

        // Update URL preview on changes
        document.getElementById('title').addEventListener('input', updateUrlPreview);
        document.getElementById('slug').addEventListener('input', updateUrlPreview);
        document.getElementById('parent_id').addEventListener('change', updateUrlPreview);

        // Initial URL preview
        updateUrlPreview();
    }
</script>
<?= $this->endSection() ?>
