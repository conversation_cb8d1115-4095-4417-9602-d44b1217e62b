<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title><?= isset($title) ? esc($title) : 'AomProuddyogiki Pvt. Ltd.' ?></title>
    <meta name="description" content="<?= isset($meta_description) ? esc($meta_description) : 'Bespoke Web & Mobile App Development, Robust Linux Hosting, and Empowering Ed-Tech Solutions for Government Exam Aspirants.' ?>">
    <meta name="keywords" content="web development, mobile app development, linux hosting, government exam preparation, indore, madhya pradesh">
    <meta name="author" content="AomProuddyogiki Pvt. Ltd.">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= isset($title) ? esc($title) : 'AomProuddyogiki Pvt. Ltd.' ?>">
    <meta property="og:description" content="<?= isset($meta_description) ? esc($meta_description) : 'Bespoke Web & Mobile App Development, Robust Linux Hosting, and Empowering Ed-Tech Solutions for Government Exam Aspirants.' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= current_url() ?>">
    <meta property="og:site_name" content="AomProuddyogiki Pvt. Ltd.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="<?= base_url('vendor/bootstrap/css/bootstrap.min.css') ?>" rel="stylesheet">

    <!-- Template CSS Files (Dynamic Loading) -->
    <?= \App\Libraries\TemplateLoader::renderCss() ?>

    <?= $this->renderSection('head') ?>
</head>
<body>
    <!-- Preloader -->
    <div id="js-preloader" class="js-preloader">
        <div class="preloader-inner">
            <span class="dot"></span>
            <div class="dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <?= $this->include('partials/navbar') ?>

    <!-- Main Content -->
    <main>
        <?= $this->renderSection('content') ?>
    </main>

    <!-- Footer -->
    <?= $this->include('partials/footer') ?>

    <!-- Scripts -->
    <script src="<?= base_url('vendor/jquery/jquery.min.js') ?>"></script>
    <script src="<?= base_url('vendor/bootstrap/js/bootstrap.bundle.min.js') ?>"></script>

    <!-- Template JavaScript Files (Dynamic Loading) -->
    <?= \App\Libraries\TemplateLoader::renderJs() ?>

    <?= $this->renderSection('scripts') ?>
</body>
</html>
