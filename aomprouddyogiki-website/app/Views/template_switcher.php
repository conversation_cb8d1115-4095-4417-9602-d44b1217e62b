<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($title) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .template-card { transition: all 0.3s ease; }
        .template-card:hover { transform: translateY(-5px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .active-template { border: 3px solid #28a745; background-color: #f8fff9; }
        .status-badge { position: absolute; top: 10px; right: 10px; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">🎨 Template Management System</h1>
                
                <!-- Alerts -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Current Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>📊 Current Template Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Active Template:</strong> <?= esc($active_template) ?><br>
                                <strong>Templates Available:</strong> <?= count($templates) ?><br>
                                <strong>Backup Enabled:</strong> <?= $template_settings['backup_enabled'] ? 'Yes' : 'No' ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Themes Directory:</strong> <?= esc($template_settings['themes_directory']) ?><br>
                                <strong>Max Size:</strong> <?= esc($template_settings['max_template_size']) ?> MB<br>
                                <strong>Auto Parse:</strong> <?= $template_settings['auto_parse_sections'] ? 'Yes' : 'No' ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Template Grid -->
                <div class="row">
                    <?php foreach ($templates as $template): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card template-card h-100 <?= $template['is_default'] ? 'active-template' : '' ?>">
                                <?php if ($template['is_default']): ?>
                                    <span class="badge bg-success status-badge">ACTIVE</span>
                                <?php endif; ?>
                                
                                <div class="card-body">
                                    <h5 class="card-title"><?= esc($template['name']) ?></h5>
                                    <p class="card-text">
                                        <small class="text-muted">Slug: <?= esc($template['slug']) ?></small><br>
                                        <?= esc($template['description']) ?>
                                    </p>
                                    
                                    <div class="mt-3">
                                        <span class="badge bg-<?= $template['status'] === 'active' ? 'success' : 'secondary' ?>">
                                            <?= strtoupper($template['status']) ?>
                                        </span>
                                        
                                        <?php if ($template['version']): ?>
                                            <span class="badge bg-info">v<?= esc($template['version']) ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($template['templatemo_id']): ?>
                                            <span class="badge bg-warning"><?= esc($template['templatemo_id']) ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="card-footer">
                                    <?php if (!$template['is_default']): ?>
                                        <form method="post" action="<?= base_url('template-switcher/switch') ?>" class="d-inline">
                                            <input type="hidden" name="template_slug" value="<?= esc($template['slug']) ?>">
                                            <button type="submit" class="btn btn-primary btn-sm">
                                                🔄 Switch to This Template
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <button class="btn btn-success btn-sm" disabled>
                                            ✅ Currently Active
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>🛠️ Template Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <a href="<?= base_url('template-switcher/refresh') ?>" class="btn btn-info">
                                    🔄 Refresh Template Cache
                                </a>
                                <a href="<?= base_url('/') ?>" class="btn btn-secondary">
                                    🏠 View Frontend
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="<?= base_url('admin/login') ?>" class="btn btn-warning">
                                    ⚙️ Admin Panel
                                </a>
                                <a href="<?= base_url('template-test') ?>" class="btn btn-info">
                                    🧪 Template Test
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
