<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($title) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .file-exists { color: #28a745; }
        .file-missing { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">🔍 Template System Status</h1>
                
                <!-- Navigation -->
                <div class="mb-4">
                    <a href="<?= base_url('template-switcher') ?>" class="btn btn-primary">← Back to Template Switcher</a>
                    <a href="<?= base_url('/') ?>" class="btn btn-secondary">🏠 Frontend</a>
                    <a href="<?= base_url('admin/login') ?>" class="btn btn-warning">⚙️ Admin</a>
                </div>

                <!-- Template Loader Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>📚 Template Loader Status</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($template_loader_test['status'] === 'success'): ?>
                            <div class="alert alert-success">
                                <strong>✅ Template Loader Working</strong><br>
                                <?= esc($template_loader_test['message']) ?>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Active Template:</strong> <?= esc($template_loader_test['template']['name'] ?? 'Unknown') ?><br>
                                    <strong>Template Slug:</strong> <?= esc($template_loader_test['template']['slug'] ?? 'Unknown') ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>CSS Assets:</strong> <?= $template_loader_test['css_count'] ?><br>
                                    <strong>JS Assets:</strong> <?= $template_loader_test['js_count'] ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <strong>❌ Template Loader Error</strong><br>
                                <?= esc($template_loader_test['message']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Database Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>🗄️ Database Status</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($database_status['status'] === 'success'): ?>
                            <div class="alert alert-success">
                                <strong>✅ Database Working</strong><br>
                                <?= esc($database_status['message']) ?>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Total Templates:</strong> <?= $database_status['template_count'] ?>
                                </div>
                                <div class="col-md-4">
                                    <strong>Active Templates:</strong> 
                                    <span class="<?= $database_status['active_templates'] === 1 ? 'status-success' : 'status-error' ?>">
                                        <?= $database_status['active_templates'] ?>
                                    </span>
                                </div>
                                <div class="col-md-4">
                                    <strong>Settings:</strong> <?= $database_status['setting_count'] ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <strong>❌ Database Error</strong><br>
                                <?= esc($database_status['message']) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- File Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>📁 Asset File Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>CSS Files</h6>
                                <?php if ($file_status['css_missing'] === 0): ?>
                                    <div class="alert alert-success">✅ All CSS files found</div>
                                <?php else: ?>
                                    <div class="alert alert-warning">⚠️ <?= $file_status['css_missing'] ?> CSS files missing</div>
                                <?php endif; ?>
                                
                                <ul class="list-unstyled">
                                    <?php foreach ($file_status['css_files'] as $file => $exists): ?>
                                        <li class="<?= $exists ? 'file-exists' : 'file-missing' ?>">
                                            <?= $exists ? '✅' : '❌' ?> <?= esc($file) ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>JavaScript Files</h6>
                                <?php if ($file_status['js_missing'] === 0): ?>
                                    <div class="alert alert-success">✅ All JS files found</div>
                                <?php else: ?>
                                    <div class="alert alert-warning">⚠️ <?= $file_status['js_missing'] ?> JS files missing</div>
                                <?php endif; ?>
                                
                                <ul class="list-unstyled">
                                    <?php foreach ($file_status['js_files'] as $file => $exists): ?>
                                        <li class="<?= $exists ? 'file-exists' : 'file-missing' ?>">
                                            <?= $exists ? '✅' : '❌' ?> <?= esc($file) ?>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>ℹ️ System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>PHP Version:</strong> <?= PHP_VERSION ?><br>
                                <strong>CodeIgniter:</strong> <?= \CodeIgniter\CodeIgniter::CI_VERSION ?><br>
                                <strong>Environment:</strong> <?= ENVIRONMENT ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Server Time:</strong> <?= date('Y-m-d H:i:s') ?><br>
                                <strong>Memory Usage:</strong> <?= round(memory_get_usage() / 1024 / 1024, 2) ?> MB<br>
                                <strong>Peak Memory:</strong> <?= round(memory_get_peak_usage() / 1024 / 1024, 2) ?> MB
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5>🛠️ Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <a href="<?= base_url('template-switcher/refresh') ?>" class="btn btn-info">🔄 Refresh Cache</a>
                        <a href="<?= base_url('template-test') ?>" class="btn btn-secondary">🧪 Template Test</a>
                        <button onclick="location.reload()" class="btn btn-primary">🔄 Reload Status</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
