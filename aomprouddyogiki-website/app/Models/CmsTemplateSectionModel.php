<?php

namespace App\Models;

use CodeIgniter\Model;

class CmsTemplateSectionModel extends Model
{
    protected $table = 'cms_template_sections';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'template_id',
        'section_name',
        'section_type',
        'html_selector',
        'file_path',
        'css_classes',
        'is_editable',
        'sort_order',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'template_id' => 'required|integer',
        'section_name' => 'required|min_length[3]|max_length[255]',
        'section_type' => 'required|in_list[header,hero,navigation,content,services,about,portfolio,testimonials,contact,footer,custom]',
        'html_selector' => 'permit_empty|max_length[500]',
        'file_path' => 'permit_empty|max_length[500]',
        'is_editable' => 'permit_empty|in_list[0,1]',
        'sort_order' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'template_id' => [
            'required' => 'Template ID is required',
            'integer' => 'Template ID must be a valid integer'
        ],
        'section_name' => [
            'required' => 'Section name is required',
            'min_length' => 'Section name must be at least 3 characters'
        ],
        'section_type' => [
            'required' => 'Section type is required',
            'in_list' => 'Invalid section type'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get sections by template ID
     */
    public function getByTemplateId($templateId)
    {
        return $this->where('template_id', $templateId)
                    ->orderBy('sort_order', 'ASC')
                    ->orderBy('section_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get sections by template ID and type
     */
    public function getByTemplateAndType($templateId, $sectionType)
    {
        return $this->where('template_id', $templateId)
                    ->where('section_type', $sectionType)
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get editable sections for a template
     */
    public function getEditableSections($templateId)
    {
        return $this->where('template_id', $templateId)
                    ->where('is_editable', true)
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get section by template and selector
     */
    public function getBySelectorAndTemplate($templateId, $htmlSelector)
    {
        return $this->where('template_id', $templateId)
                    ->where('html_selector', $htmlSelector)
                    ->first();
    }

    /**
     * Update section order
     */
    public function updateSectionOrder($sectionId, $newOrder)
    {
        return $this->update($sectionId, ['sort_order' => $newOrder]);
    }

    /**
     * Bulk update section orders
     */
    public function updateSectionOrders($templateId, $orderData)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            foreach ($orderData as $sectionId => $order) {
                $this->where('id', $sectionId)
                     ->where('template_id', $templateId)
                     ->set('sort_order', $order)
                     ->update();
            }

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }

    /**
     * Get sections grouped by type
     */
    public function getSectionsGroupedByType($templateId)
    {
        $sections = $this->getByTemplateId($templateId);
        $grouped = [];

        foreach ($sections as $section) {
            $type = $section['section_type'];
            if (!isset($grouped[$type])) {
                $grouped[$type] = [];
            }
            $grouped[$type][] = $section;
        }

        return $grouped;
    }

    /**
     * Clone sections from one template to another
     */
    public function cloneSections($sourceTemplateId, $targetTemplateId)
    {
        $sourceSections = $this->getByTemplateId($sourceTemplateId);
        
        if (empty($sourceSections)) {
            return true; // No sections to clone
        }

        $db = \Config\Database::connect();
        $db->transStart();

        try {
            foreach ($sourceSections as $section) {
                unset($section['id']); // Remove ID for new insert
                $section['template_id'] = $targetTemplateId;
                $section['created_at'] = date('Y-m-d H:i:s');
                $section['updated_at'] = date('Y-m-d H:i:s');
                
                $this->insert($section);
            }

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }

    /**
     * Get section types with counts
     */
    public function getSectionTypeCounts($templateId)
    {
        return $this->select('section_type, COUNT(*) as count')
                    ->where('template_id', $templateId)
                    ->groupBy('section_type')
                    ->findAll();
    }

    /**
     * Search sections within a template
     */
    public function searchSections($templateId, $query)
    {
        return $this->where('template_id', $templateId)
                    ->groupStart()
                    ->like('section_name', $query)
                    ->orLike('section_type', $query)
                    ->orLike('html_selector', $query)
                    ->orLike('css_classes', $query)
                    ->groupEnd()
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get next sort order for a template
     */
    public function getNextSortOrder($templateId)
    {
        $maxOrder = $this->selectMax('sort_order')
                         ->where('template_id', $templateId)
                         ->get()
                         ->getRowArray();

        return ($maxOrder['sort_order'] ?? 0) + 1;
    }

    /**
     * Duplicate section within same template
     */
    public function duplicateSection($sectionId)
    {
        $section = $this->find($sectionId);
        
        if (!$section) {
            return false;
        }

        unset($section['id']);
        $section['section_name'] .= ' (Copy)';
        $section['sort_order'] = $this->getNextSortOrder($section['template_id']);
        $section['created_at'] = date('Y-m-d H:i:s');
        $section['updated_at'] = date('Y-m-d H:i:s');

        return $this->insert($section);
    }

    /**
     * Get sections for template preview
     */
    public function getPreviewSections($templateId)
    {
        return $this->select('section_name, section_type, html_selector, css_classes, sort_order')
                    ->where('template_id', $templateId)
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Validate section structure for template
     */
    public function validateTemplateStructure($templateId)
    {
        $sections = $this->getByTemplateId($templateId);
        $requiredTypes = ['header', 'footer'];
        $foundTypes = array_column($sections, 'section_type');

        $missing = array_diff($requiredTypes, $foundTypes);
        
        return [
            'valid' => empty($missing),
            'missing_sections' => $missing,
            'total_sections' => count($sections),
            'editable_sections' => count(array_filter($sections, function($s) { return $s['is_editable']; }))
        ];
    }

    /**
     * Get section statistics for template
     */
    public function getSectionStats($templateId)
    {
        $stats = [];
        
        $stats['total'] = $this->where('template_id', $templateId)->countAllResults(false);
        $stats['editable'] = $this->where('template_id', $templateId)
                                  ->where('is_editable', true)
                                  ->countAllResults(false);
        $stats['non_editable'] = $stats['total'] - $stats['editable'];
        
        // Get type distribution
        $typeStats = $this->getSectionTypeCounts($templateId);
        $stats['by_type'] = [];
        foreach ($typeStats as $typeStat) {
            $stats['by_type'][$typeStat['section_type']] = $typeStat['count'];
        }
        
        return $stats;
    }
}
