<?php

namespace App\Models;

use CodeIgniter\Model;

class CmsTemplateModel extends Model
{
    protected $table = 'cms_templates';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name',
        'slug',
        'description',
        'version',
        'author',
        'source_url',
        'templatemo_id',
        'preview_image',
        'folder_path',
        'config_data',
        'status',
        'is_default',
        'installation_date',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|min_length[3]|max_length[255]',
        'slug' => 'required|min_length[3]|max_length[255]|is_unique[cms_templates.slug,id,{id}]',
        'version' => 'required|max_length[50]',
        'folder_path' => 'required|max_length[500]',
        'status' => 'required|in_list[active,inactive,installing,error]',
        'is_default' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Template name is required',
            'min_length' => 'Template name must be at least 3 characters',
            'max_length' => 'Template name cannot exceed 255 characters'
        ],
        'slug' => [
            'required' => 'Template slug is required',
            'is_unique' => 'Template slug must be unique'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateSlug', 'parseConfigData'];
    protected $beforeUpdate = ['generateSlug', 'parseConfigData'];

    /**
     * Generate slug from name if not provided
     */
    protected function generateSlug(array $data)
    {
        if (isset($data['data']['name']) && empty($data['data']['slug'])) {
            $data['data']['slug'] = url_title($data['data']['name'], '-', true);
        }
        return $data;
    }

    /**
     * Parse config data to JSON before saving
     */
    protected function parseConfigData(array $data)
    {
        if (isset($data['data']['config_data']) && is_array($data['data']['config_data'])) {
            $data['data']['config_data'] = json_encode($data['data']['config_data']);
        }
        return $data;
    }

    /**
     * Parse config data from JSON string
     */
    protected function parseConfigJson($data)
    {
        if ($data === null) {
            return null;
        }

        if (is_array($data)) {
            // Check if it's an array of records or a single record
            if (isset($data[0]) && is_array($data[0])) {
                // Multiple records
                foreach ($data as &$record) {
                    if (isset($record['config_data']) && is_string($record['config_data'])) {
                        $record['config_data'] = json_decode($record['config_data'], true);
                    }
                }
            } else {
                // Single record as array
                if (isset($data['config_data']) && is_string($data['config_data'])) {
                    $data['config_data'] = json_decode($data['config_data'], true);
                }
            }
        }
        return $data;
    }

    /**
     * Get active template
     */
    public function getActiveTemplate()
    {
        $result = $this->where('status', 'active')
                       ->where('is_default', true)
                       ->first();

        return $result ? $this->parseConfigJson($result) : null;
    }

    /**
     * Get all available templates
     */
    public function getAvailableTemplates()
    {
        $results = $this->whereIn('status', ['active', 'inactive'])
                        ->orderBy('is_default', 'DESC')
                        ->orderBy('name', 'ASC')
                        ->findAll();

        return $this->parseConfigJson($results);
    }

    /**
     * Get template by slug
     */
    public function getBySlug($slug)
    {
        $result = $this->where('slug', $slug)->first();

        return $result ? $this->parseConfigJson($result) : null;
    }

    /**
     * Set template as active
     */
    public function setActiveTemplate($templateId)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Verify the template exists and is ready for activation
            $newTemplate = $this->find($templateId);
            if (!$newTemplate) {
                throw new \Exception('Template not found');
            }

            if (!in_array($newTemplate['status'], ['active', 'inactive'])) {
                throw new \Exception('Template is not ready for activation');
            }

            // Deactivate all templates
            $this->set('is_default', false)
                 ->where('is_default', true)
                 ->update();

            // Activate selected template
            $this->set([
                'is_default' => true,
                'status' => 'active'
            ])
            ->where('id', $templateId)
            ->update();

            // Verify that exactly one template is now active
            $activeCount = $this->where('is_default', true)->countAllResults(false);
            if ($activeCount !== 1) {
                throw new \Exception('Failed to set exactly one active template');
            }

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            log_message('error', 'setActiveTemplate failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get template with sections and assets
     */
    public function getTemplateWithDetails($templateId)
    {
        $template = $this->find($templateId);

        if (!$template) {
            return null;
        }

        // Parse config data
        $template = $this->parseConfigJson($template);

        // Get sections
        $sectionModel = new CmsTemplateSectionModel();
        $template['sections'] = $sectionModel->getByTemplateId($templateId);

        // Get assets
        $assetModel = new CmsTemplateAssetModel();
        $template['assets'] = $assetModel->getByTemplateId($templateId);

        return $template;
    }

    /**
     * Install new template
     */
    public function installTemplate($templateData)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Set status to installing
            $templateData['status'] = 'installing';
            $templateData['installation_date'] = date('Y-m-d H:i:s');

            $templateId = $this->insert($templateData);

            if (!$templateId) {
                throw new \Exception('Failed to insert template');
            }

            $db->transComplete();
            return $templateId;
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }

    /**
     * Update template status
     */
    public function updateStatus($templateId, $status)
    {
        return $this->update($templateId, ['status' => $status]);
    }

    /**
     * Get templates by status
     */
    public function getByStatus($status)
    {
        return $this->where('status', $status)->findAll();
    }

    /**
     * Search templates
     */
    public function searchTemplates($query)
    {
        return $this->groupStart()
                    ->like('name', $query)
                    ->orLike('description', $query)
                    ->orLike('author', $query)
                    ->orLike('templatemo_id', $query)
                    ->groupEnd()
                    ->whereIn('status', ['active', 'inactive'])
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get template statistics
     */
    public function getTemplateStats()
    {
        $stats = [];
        
        $stats['total'] = $this->countAll();
        $stats['active'] = $this->where('status', 'active')->countAllResults(false);
        $stats['inactive'] = $this->where('status', 'inactive')->countAllResults(false);
        $stats['installing'] = $this->where('status', 'installing')->countAllResults(false);
        $stats['error'] = $this->where('status', 'error')->countAllResults(false);
        
        return $stats;
    }

    /**
     * Delete template and related data
     */
    public function deleteTemplate($templateId)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Delete assets
            $assetModel = new CmsTemplateAssetModel();
            $assetModel->where('template_id', $templateId)->delete();

            // Delete sections
            $sectionModel = new CmsTemplateSectionModel();
            $sectionModel->where('template_id', $templateId)->delete();

            // Delete template
            $this->delete($templateId);

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }
}
