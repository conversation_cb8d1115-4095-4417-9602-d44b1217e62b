<?php

namespace App\Models;

use CodeIgniter\Model;

class CmsTemplateAssetModel extends Model
{
    protected $table = 'cms_template_assets';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'template_id',
        'asset_type',
        'file_name',
        'file_path',
        'file_size',
        'is_critical',
        'load_order',
        'created_at'
    ];

    // Dates
    protected $useTimestamps = false; // Only created_at, no updated_at
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';

    // Validation
    protected $validationRules = [
        'template_id' => 'required|integer',
        'asset_type' => 'required|in_list[css,js,image,font,other]',
        'file_name' => 'required|min_length[1]|max_length[255]',
        'file_path' => 'required|min_length[1]|max_length[500]',
        'file_size' => 'permit_empty|integer',
        'is_critical' => 'permit_empty|in_list[0,1]',
        'load_order' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'template_id' => [
            'required' => 'Template ID is required',
            'integer' => 'Template ID must be a valid integer'
        ],
        'asset_type' => [
            'required' => 'Asset type is required',
            'in_list' => 'Invalid asset type'
        ],
        'file_name' => [
            'required' => 'File name is required'
        ],
        'file_path' => [
            'required' => 'File path is required'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setCreatedAt'];

    /**
     * Set created_at timestamp
     */
    protected function setCreatedAt(array $data)
    {
        $data['data']['created_at'] = date('Y-m-d H:i:s');
        return $data;
    }

    /**
     * Get assets by template ID
     */
    public function getByTemplateId($templateId)
    {
        return $this->where('template_id', $templateId)
                    ->orderBy('asset_type', 'ASC')
                    ->orderBy('load_order', 'ASC')
                    ->orderBy('file_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get assets by template ID and type
     */
    public function getByTemplateAndType($templateId, $assetType)
    {
        return $this->where('template_id', $templateId)
                    ->where('asset_type', $assetType)
                    ->orderBy('load_order', 'ASC')
                    ->orderBy('file_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get critical assets for a template
     */
    public function getCriticalAssets($templateId)
    {
        return $this->where('template_id', $templateId)
                    ->where('is_critical', true)
                    ->orderBy('asset_type', 'ASC')
                    ->orderBy('load_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get CSS assets for a template
     */
    public function getCssAssets($templateId)
    {
        return $this->getByTemplateAndType($templateId, 'css');
    }

    /**
     * Get JavaScript assets for a template
     */
    public function getJsAssets($templateId)
    {
        return $this->getByTemplateAndType($templateId, 'js');
    }

    /**
     * Get image assets for a template
     */
    public function getImageAssets($templateId)
    {
        return $this->getByTemplateAndType($templateId, 'image');
    }

    /**
     * Get font assets for a template
     */
    public function getFontAssets($templateId)
    {
        return $this->getByTemplateAndType($templateId, 'font');
    }

    /**
     * Get assets grouped by type
     */
    public function getAssetsGroupedByType($templateId)
    {
        $assets = $this->getByTemplateId($templateId);
        $grouped = [];

        foreach ($assets as $asset) {
            $type = $asset['asset_type'];
            if (!isset($grouped[$type])) {
                $grouped[$type] = [];
            }
            $grouped[$type][] = $asset;
        }

        return $grouped;
    }

    /**
     * Update asset load order
     */
    public function updateLoadOrder($assetId, $newOrder)
    {
        return $this->update($assetId, ['load_order' => $newOrder]);
    }

    /**
     * Bulk update asset load orders
     */
    public function updateLoadOrders($templateId, $assetType, $orderData)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            foreach ($orderData as $assetId => $order) {
                $this->where('id', $assetId)
                     ->where('template_id', $templateId)
                     ->where('asset_type', $assetType)
                     ->set('load_order', $order)
                     ->update();
            }

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }

    /**
     * Clone assets from one template to another
     */
    public function cloneAssets($sourceTemplateId, $targetTemplateId)
    {
        $sourceAssets = $this->getByTemplateId($sourceTemplateId);
        
        if (empty($sourceAssets)) {
            return true; // No assets to clone
        }

        $db = \Config\Database::connect();
        $db->transStart();

        try {
            foreach ($sourceAssets as $asset) {
                unset($asset['id']); // Remove ID for new insert
                $asset['template_id'] = $targetTemplateId;
                $asset['created_at'] = date('Y-m-d H:i:s');
                
                $this->insert($asset);
            }

            $db->transComplete();
            return $db->transStatus();
        } catch (\Exception $e) {
            $db->transRollback();
            return false;
        }
    }

    /**
     * Get asset statistics for template
     */
    public function getAssetStats($templateId)
    {
        $stats = [];
        
        $stats['total'] = $this->where('template_id', $templateId)->countAllResults(false);
        $stats['critical'] = $this->where('template_id', $templateId)
                                  ->where('is_critical', true)
                                  ->countAllResults(false);
        $stats['non_critical'] = $stats['total'] - $stats['critical'];
        
        // Get type distribution
        $typeStats = $this->select('asset_type, COUNT(*) as count')
                          ->where('template_id', $templateId)
                          ->groupBy('asset_type')
                          ->findAll();
        
        $stats['by_type'] = [];
        foreach ($typeStats as $typeStat) {
            $stats['by_type'][$typeStat['asset_type']] = $typeStat['count'];
        }
        
        // Get total file size
        $sizeResult = $this->selectSum('file_size')
                           ->where('template_id', $templateId)
                           ->get()
                           ->getRowArray();
        
        $stats['total_size'] = $sizeResult['file_size'] ?? 0;
        
        return $stats;
    }

    /**
     * Get next load order for asset type
     */
    public function getNextLoadOrder($templateId, $assetType)
    {
        $maxOrder = $this->selectMax('load_order')
                         ->where('template_id', $templateId)
                         ->where('asset_type', $assetType)
                         ->get()
                         ->getRowArray();

        return ($maxOrder['load_order'] ?? 0) + 1;
    }

    /**
     * Check if asset exists
     */
    public function assetExists($templateId, $filePath)
    {
        return $this->where('template_id', $templateId)
                    ->where('file_path', $filePath)
                    ->countAllResults() > 0;
    }

    /**
     * Get asset by file path
     */
    public function getByFilePath($templateId, $filePath)
    {
        return $this->where('template_id', $templateId)
                    ->where('file_path', $filePath)
                    ->first();
    }

    /**
     * Update file size for asset
     */
    public function updateFileSize($assetId, $fileSize)
    {
        return $this->update($assetId, ['file_size' => $fileSize]);
    }

    /**
     * Mark asset as critical
     */
    public function markAsCritical($assetId, $isCritical = true)
    {
        return $this->update($assetId, ['is_critical' => $isCritical]);
    }

    /**
     * Get assets for HTML head section (CSS and critical JS)
     */
    public function getHeadAssets($templateId)
    {
        return $this->where('template_id', $templateId)
                    ->groupStart()
                    ->where('asset_type', 'css')
                    ->orWhere('is_critical', true)
                    ->groupEnd()
                    ->orderBy('asset_type', 'ASC')
                    ->orderBy('load_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get assets for HTML body section (non-critical JS)
     */
    public function getBodyAssets($templateId)
    {
        return $this->where('template_id', $templateId)
                    ->where('asset_type', 'js')
                    ->where('is_critical', false)
                    ->orderBy('load_order', 'ASC')
                    ->findAll();
    }

    /**
     * Search assets within a template
     */
    public function searchAssets($templateId, $query)
    {
        return $this->where('template_id', $templateId)
                    ->groupStart()
                    ->like('file_name', $query)
                    ->orLike('file_path', $query)
                    ->groupEnd()
                    ->orderBy('asset_type', 'ASC')
                    ->orderBy('file_name', 'ASC')
                    ->findAll();
    }
}
