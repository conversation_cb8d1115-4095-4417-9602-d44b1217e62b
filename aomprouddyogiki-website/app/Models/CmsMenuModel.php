<?php

namespace App\Models;

use CodeIgniter\Model;

class CmsMenuModel extends Model
{
    protected $table = 'cms_menus';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'title',
        'url',
        'page_id',
        'target',
        'icon_class',
        'parent_id',
        'sort_order',
        'menu_location',
        'status',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'title' => 'required|min_length[2]|max_length[255]',
        'url' => 'required|max_length[255]',
        'target' => 'required|in_list[_self,_blank]',
        'menu_location' => 'required|in_list[primary,footer,sidebar]',
        'status' => 'required|in_list[active,inactive]',
        'sort_order' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'title' => [
            'required' => 'Menu title is required',
            'min_length' => 'Menu title must be at least 2 characters long',
            'max_length' => 'Menu title cannot exceed 255 characters'
        ],
        'url' => [
            'required' => 'Menu URL is required',
            'max_length' => 'Menu URL cannot exceed 255 characters'
        ],
        'target' => [
            'required' => 'Menu target is required'
        ],
        'menu_location' => [
            'required' => 'Menu location is required'
        ],
        'status' => [
            'required' => 'Menu status is required'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get menu items by location
     */
    public function getByLocation($location = 'primary')
    {
        return $this->where('menu_location', $location)
                    ->where('status', 'active')
                    ->orderBy('sort_order', 'ASC')
                    ->orderBy('title', 'ASC')
                    ->findAll();
    }

    /**
     * Get hierarchical menu structure
     */
    public function getHierarchicalMenu($location = 'primary')
    {
        $menuItems = $this->getByLocation($location);
        return $this->buildMenuTree($menuItems);
    }

    /**
     * Build menu tree structure
     */
    private function buildMenuTree($menuItems, $parentId = null)
    {
        $tree = [];
        
        foreach ($menuItems as $item) {
            if ($item['parent_id'] == $parentId) {
                $item['children'] = $this->buildMenuTree($menuItems, $item['id']);
                $tree[] = $item;
            }
        }
        
        return $tree;
    }

    /**
     * Get parent menu items
     */
    public function getParentMenus($location = 'primary')
    {
        return $this->where('parent_id', null)
                    ->where('menu_location', $location)
                    ->where('status', 'active')
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get child menu items
     */
    public function getChildMenus($parentId)
    {
        return $this->where('parent_id', $parentId)
                    ->where('status', 'active')
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Update menu order
     */
    public function updateMenuOrder($menuData)
    {
        foreach ($menuData as $order => $menuId) {
            $this->update($menuId, ['sort_order' => $order + 1]);
        }
        return true;
    }

    /**
     * Get menu statistics
     */
    public function getStatistics()
    {
        $stats = [];
        $stats['total'] = $this->countAll();
        $stats['active'] = $this->where('status', 'active')->countAllResults(false);
        $stats['inactive'] = $this->where('status', 'inactive')->countAllResults(false);
        $stats['primary'] = $this->where('menu_location', 'primary')->countAllResults(false);
        $stats['footer'] = $this->where('menu_location', 'footer')->countAllResults(false);
        
        return $stats;
    }

    /**
     * Check if menu has children
     */
    public function hasChildren($menuId)
    {
        return $this->where('parent_id', $menuId)->countAllResults() > 0;
    }

    /**
     * Get menus with page information
     */
    public function getMenusWithPages($location = 'primary')
    {
        return $this->select('cms_menus.*, cms_pages.title as page_title, cms_pages.slug as page_slug, cms_pages.status as page_status')
                    ->join('cms_pages', 'cms_pages.id = cms_menus.page_id', 'left')
                    ->where('cms_menus.menu_location', $location)
                    ->orderBy('cms_menus.sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get menu by page ID
     */
    public function getByPageId($pageId)
    {
        return $this->where('page_id', $pageId)->findAll();
    }

    /**
     * Link menu to page
     */
    public function linkToPage($menuId, $pageId)
    {
        return $this->update($menuId, ['page_id' => $pageId]);
    }

    /**
     * Unlink menu from page
     */
    public function unlinkFromPage($menuId)
    {
        return $this->update($menuId, ['page_id' => null]);
    }

    /**
     * Get unlinked menus (not associated with any page)
     */
    public function getUnlinkedMenus($location = 'primary')
    {
        return $this->where('page_id', null)
                    ->where('menu_location', $location)
                    ->where('status', 'active')
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Get linked menus (associated with pages)
     */
    public function getLinkedMenus($location = 'primary')
    {
        return $this->where('page_id IS NOT NULL')
                    ->where('menu_location', $location)
                    ->where('status', 'active')
                    ->orderBy('sort_order', 'ASC')
                    ->findAll();
    }

    /**
     * Create menu item for page
     */
    public function createForPage($pageData, $menuLocation = 'primary', $parentId = null)
    {
        $menuData = [
            'title' => $pageData['title'],
            'url' => '/' . $pageData['full_slug'],
            'page_id' => $pageData['id'],
            'target' => '_self',
            'parent_id' => $parentId,
            'sort_order' => $this->getNextSortOrder($menuLocation, $parentId),
            'menu_location' => $menuLocation,
            'status' => 'active'
        ];

        return $this->insert($menuData);
    }

    /**
     * Get next sort order for menu location
     */
    protected function getNextSortOrder($location, $parentId = null)
    {
        $query = $this->where('menu_location', $location);

        if ($parentId) {
            $query->where('parent_id', $parentId);
        } else {
            $query->where('parent_id', null);
        }

        $maxOrder = $query->selectMax('sort_order')->first();
        return ($maxOrder['sort_order'] ?? 0) + 1;
    }

    /**
     * Update menu URL when page slug changes
     */
    public function updatePageMenuUrls($pageId, $newFullSlug)
    {
        $menus = $this->where('page_id', $pageId)->findAll();

        foreach ($menus as $menu) {
            $this->update($menu['id'], ['url' => '/' . $newFullSlug]);
        }

        return true;
    }

    /**
     * Get menu association statistics
     */
    public function getAssociationStatistics()
    {
        $stats = $this->getStatistics();
        $stats['linked'] = $this->where('page_id IS NOT NULL')->countAllResults(false);
        $stats['unlinked'] = $this->where('page_id', null)->countAllResults(false);

        return $stats;
    }
}
