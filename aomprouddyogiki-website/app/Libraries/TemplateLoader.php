<?php

namespace App\Libraries;

class TemplateLoader
{
    protected static $activeTemplate = null;
    protected static $cssAssets = null;
    protected static $jsAssets = null;

    /**
     * Get the active template from database
     */
    public static function getActiveTemplate()
    {
        if (self::$activeTemplate === null) {
            try {
                $db = \Config\Database::connect();
                $query = $db->query("SELECT id, name, slug FROM cms_templates WHERE is_default = 1 LIMIT 1");
                self::$activeTemplate = $query->getRowArray();
                
                if (!self::$activeTemplate) {
                    // Fallback to Space Dynamic
                    self::$activeTemplate = [
                        'id' => 1,
                        'name' => 'Space Dynamic',
                        'slug' => 'space-dynamic'
                    ];
                }
            } catch (\Exception $e) {
                // Fallback to Space Dynamic
                self::$activeTemplate = [
                    'id' => 1,
                    'name' => 'Space Dynamic',
                    'slug' => 'space-dynamic'
                ];
            }
        }
        
        return self::$activeTemplate;
    }

    /**
     * Get CSS assets for active template
     */
    public static function getCssAssets()
    {
        if (self::$cssAssets === null) {
            $template = self::getActiveTemplate();
            
            try {
                $db = \Config\Database::connect();
                $query = $db->query("
                    SELECT file_path, load_order 
                    FROM cms_template_assets 
                    WHERE template_id = ? AND asset_type = 'css' 
                    ORDER BY load_order
                ", [$template['id']]);
                
                $assets = $query->getResultArray();
                
                if (!empty($assets)) {
                    self::$cssAssets = $assets;
                } else {
                    self::$cssAssets = self::getDefaultCssAssets();
                }
            } catch (\Exception $e) {
                self::$cssAssets = self::getDefaultCssAssets();
            }
        }
        
        return self::$cssAssets;
    }

    /**
     * Get JS assets for active template
     */
    public static function getJsAssets()
    {
        if (self::$jsAssets === null) {
            $template = self::getActiveTemplate();
            
            try {
                $db = \Config\Database::connect();
                $query = $db->query("
                    SELECT file_path, load_order 
                    FROM cms_template_assets 
                    WHERE template_id = ? AND asset_type = 'js' 
                    ORDER BY load_order
                ", [$template['id']]);
                
                $assets = $query->getResultArray();
                
                if (!empty($assets)) {
                    self::$jsAssets = $assets;
                } else {
                    self::$jsAssets = self::getDefaultJsAssets();
                }
            } catch (\Exception $e) {
                self::$jsAssets = self::getDefaultJsAssets();
            }
        }
        
        return self::$jsAssets;
    }

    /**
     * Get default CSS assets (Space Dynamic)
     */
    protected static function getDefaultCssAssets()
    {
        return [
            ['file_path' => 'css/fontawesome.css', 'load_order' => 1],
            ['file_path' => 'css/templatemo-space-dynamic.css', 'load_order' => 2],
            ['file_path' => 'css/animated.css', 'load_order' => 3],
            ['file_path' => 'css/owl.css', 'load_order' => 4],
        ];
    }

    /**
     * Get default JS assets (Space Dynamic)
     */
    protected static function getDefaultJsAssets()
    {
        return [
            ['file_path' => 'js/isotope.js', 'load_order' => 1],
            ['file_path' => 'js/owl-carousel.js', 'load_order' => 2],
            ['file_path' => 'js/animation.js', 'load_order' => 3],
            ['file_path' => 'js/templatemo-custom.js', 'load_order' => 4],
        ];
    }

    /**
     * Render CSS HTML tags
     */
    public static function renderCss()
    {
        $assets = self::getCssAssets();
        $html = '';
        
        foreach ($assets as $asset) {
            $html .= '<link rel="stylesheet" href="' . base_url($asset['file_path']) . '">' . "\n    ";
        }
        
        return trim($html);
    }

    /**
     * Render JS HTML tags
     */
    public static function renderJs()
    {
        $assets = self::getJsAssets();
        $html = '';
        
        foreach ($assets as $asset) {
            $html .= '<script src="' . base_url($asset['file_path']) . '"></script>' . "\n    ";
        }
        
        return trim($html);
    }

    /**
     * Clear cache (useful after template switching)
     */
    public static function clearCache()
    {
        self::$activeTemplate = null;
        self::$cssAssets = null;
        self::$jsAssets = null;
    }

    /**
     * Check if template switching is needed
     */
    public static function checkTemplateSwitch()
    {
        // Clear cache to force reload from database
        self::clearCache();
        return self::getActiveTemplate();
    }
}
