<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class RegisterSpaceDynamicTemplate extends Migration
{
    public function up()
    {
        // Register the current Space Dynamic template
        $templateId = $this->db->table('cms_templates')->insert([
            'name' => 'Space Dynamic',
            'slug' => 'space-dynamic',
            'description' => 'Professional business template with modern design, perfect for web development companies. Features responsive layout, smooth animations, and clean typography.',
            'version' => '1.0.0',
            'author' => 'TemplateMo',
            'source_url' => 'https://templatemo.com/tm-562-space-dynamic',
            'templatemo_id' => 'tm-562',
            'preview_image' => 'themes/space-dynamic/preview.jpg',
            'folder_path' => 'themes/space-dynamic',
            'config_data' => json_encode([
                'bootstrap_version' => '5.0',
                'jquery_required' => true,
                'responsive' => true,
                'color_scheme' => 'blue-gradient',
                'layout_type' => 'single-page',
                'sections' => ['header', 'hero', 'services', 'about', 'portfolio', 'contact', 'footer']
            ]),
            'status' => 'active',
            'is_default' => true,
            'installation_date' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
        ]);

        // Get the inserted template ID
        $templateId = $this->db->insertID();

        // Register template sections
        $sections = [
            [
                'template_id' => $templateId,
                'section_name' => 'Header Navigation',
                'section_type' => 'header',
                'html_selector' => 'header.header-area',
                'file_path' => 'partials/navbar.php',
                'css_classes' => 'header-area header-sticky',
                'is_editable' => true,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'section_name' => 'Main Banner Hero',
                'section_type' => 'hero',
                'html_selector' => '.main-banner',
                'file_path' => 'sections/hero.php',
                'css_classes' => 'main-banner',
                'is_editable' => true,
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'section_name' => 'Services Section',
                'section_type' => 'services',
                'html_selector' => '#services',
                'file_path' => 'sections/services.php',
                'css_classes' => 'services section',
                'is_editable' => true,
                'sort_order' => 3,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'section_name' => 'About Section',
                'section_type' => 'about',
                'html_selector' => '#about',
                'file_path' => 'sections/about.php',
                'css_classes' => 'about-us section',
                'is_editable' => true,
                'sort_order' => 4,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'section_name' => 'Portfolio Section',
                'section_type' => 'portfolio',
                'html_selector' => '#portfolio',
                'file_path' => 'sections/portfolio.php',
                'css_classes' => 'portfolio-section section',
                'is_editable' => true,
                'sort_order' => 5,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'section_name' => 'Contact Section',
                'section_type' => 'contact',
                'html_selector' => '#contact',
                'file_path' => 'sections/contact.php',
                'css_classes' => 'contact-us section',
                'is_editable' => true,
                'sort_order' => 6,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'section_name' => 'Footer',
                'section_type' => 'footer',
                'html_selector' => 'footer',
                'file_path' => 'partials/footer.php',
                'css_classes' => 'footer',
                'is_editable' => true,
                'sort_order' => 7,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('cms_template_sections')->insertBatch($sections);

        // Register template assets
        $assets = [
            // CSS Assets
            [
                'template_id' => $templateId,
                'asset_type' => 'css',
                'file_name' => 'templatemo-space-dynamic.css',
                'file_path' => 'css/templatemo-space-dynamic.css',
                'is_critical' => true,
                'load_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'asset_type' => 'css',
                'file_name' => 'fontawesome.css',
                'file_path' => 'css/fontawesome.css',
                'is_critical' => true,
                'load_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'asset_type' => 'css',
                'file_name' => 'animated.css',
                'file_path' => 'css/animated.css',
                'is_critical' => false,
                'load_order' => 3,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'asset_type' => 'css',
                'file_name' => 'owl.css',
                'file_path' => 'css/owl.css',
                'is_critical' => false,
                'load_order' => 4,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            // JavaScript Assets
            [
                'template_id' => $templateId,
                'asset_type' => 'js',
                'file_name' => 'templatemo-custom.js',
                'file_path' => 'js/templatemo-custom.js',
                'is_critical' => true,
                'load_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'asset_type' => 'js',
                'file_name' => 'animation.js',
                'file_path' => 'js/animation.js',
                'is_critical' => false,
                'load_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'asset_type' => 'js',
                'file_name' => 'owl-carousel.js',
                'file_path' => 'js/owl-carousel.js',
                'is_critical' => false,
                'load_order' => 3,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'template_id' => $templateId,
                'asset_type' => 'js',
                'file_name' => 'imagesloaded.js',
                'file_path' => 'js/imagesloaded.js',
                'is_critical' => false,
                'load_order' => 4,
                'created_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('cms_template_assets')->insertBatch($assets);
    }

    public function down()
    {
        // Remove Space Dynamic template and related data
        $template = $this->db->table('cms_templates')->where('slug', 'space-dynamic')->get()->getRowArray();
        
        if ($template) {
            $templateId = $template['id'];
            
            // Delete assets
            $this->db->table('cms_template_assets')->where('template_id', $templateId)->delete();
            
            // Delete sections
            $this->db->table('cms_template_sections')->where('template_id', $templateId)->delete();
            
            // Delete template
            $this->db->table('cms_templates')->where('id', $templateId)->delete();
        }
    }
}
