<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddPageHierarchyAndRoutes extends Migration
{
    public function up()
    {
        // Check and add foreign key for parent pages if not exists
        $foreignKeys = $this->db->query("SELECT CONSTRAINT_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'cms_pages' AND CONSTRAINT_TYPE = 'FOREIGN KEY' AND CONSTRAINT_NAME = 'fk_cms_pages_parent_id'")->getResult();

        if (empty($foreignKeys)) {
            $this->db->query('ALTER TABLE cms_pages ADD CONSTRAINT fk_cms_pages_parent_id FOREIGN KEY (parent_id) REFERENCES cms_pages(id) ON DELETE SET NULL ON UPDATE CASCADE');
        }

        // Check and add index for full_slug if not exists
        $indexes = $this->db->query("SHOW INDEX FROM cms_pages WHERE Key_name = 'idx_cms_pages_full_slug'")->getResult();

        if (empty($indexes)) {
            $this->db->query('ALTER TABLE cms_pages ADD INDEX idx_cms_pages_full_slug (full_slug)');
        }

        // Add foreign key constraint for cms_menus page_id (if not exists)
        $menuForeignKeys = $this->db->query("SELECT CONSTRAINT_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'cms_menus' AND CONSTRAINT_TYPE = 'FOREIGN KEY' AND CONSTRAINT_NAME = 'fk_cms_menus_page_id'")->getResult();

        if (empty($menuForeignKeys)) {
            $this->db->query('ALTER TABLE cms_menus ADD CONSTRAINT fk_cms_menus_page_id FOREIGN KEY (page_id) REFERENCES cms_pages(id) ON DELETE SET NULL ON UPDATE CASCADE');
        }

        // Create dynamic_routes table for route caching
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'route_pattern' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => false
            ],
            'controller_method' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false
            ],
            'page_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false
            ],
            'route_type' => [
                'type' => 'ENUM',
                'constraint' => ['page', 'hierarchical'],
                'default' => 'page'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'default' => 'active'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true
            ]
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('route_pattern');
        $this->forge->addKey('page_id');
        $this->forge->addKey('status');
        $this->forge->addForeignKey('page_id', 'cms_pages', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('dynamic_routes');

        // Update existing pages with full_slug based on current slug
        $this->db->query('UPDATE cms_pages SET full_slug = slug WHERE full_slug IS NULL');
    }

    public function down()
    {
        // Drop dynamic_routes table
        $this->forge->dropTable('dynamic_routes');

        // Remove foreign key from cms_pages if it exists
        $foreignKeys = $this->db->query("SELECT CONSTRAINT_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'cms_pages' AND CONSTRAINT_TYPE = 'FOREIGN KEY' AND CONSTRAINT_NAME = 'fk_cms_pages_parent_id'")->getResult();

        if (!empty($foreignKeys)) {
            $this->db->query('ALTER TABLE cms_pages DROP FOREIGN KEY fk_cms_pages_parent_id');
        }

        // Remove foreign key from cms_menus if it exists
        $menuForeignKeys = $this->db->query("SELECT CONSTRAINT_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'cms_menus' AND CONSTRAINT_TYPE = 'FOREIGN KEY' AND CONSTRAINT_NAME = 'fk_cms_menus_page_id'")->getResult();

        if (!empty($menuForeignKeys)) {
            $this->db->query('ALTER TABLE cms_menus DROP FOREIGN KEY fk_cms_menus_page_id');
        }
    }
}
