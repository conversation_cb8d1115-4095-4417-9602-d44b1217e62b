<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateTemplateEngineTablesTable extends Migration
{
    public function up()
    {
        // Templates table - stores template metadata
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'slug' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'version' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'default' => '1.0.0',
            ],
            'author' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'source_url' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'templatemo_id' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'preview_image' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'folder_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
            ],
            'config_data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive', 'installing', 'error'],
                'default' => 'inactive',
            ],
            'is_default' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'installation_date' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('slug');
        $this->forge->addKey('status');
        $this->forge->addKey('templatemo_id');
        $this->forge->createTable('cms_templates');

        // Template sections table - maps template sections
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'section_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'section_type' => [
                'type' => 'ENUM',
                'constraint' => ['header', 'hero', 'navigation', 'content', 'services', 'about', 'portfolio', 'testimonials', 'contact', 'footer', 'custom'],
                'default' => 'custom',
            ],
            'html_selector' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'file_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'css_classes' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'is_editable' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'sort_order' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['template_id', 'section_type']);
        $this->forge->addForeignKey('template_id', 'cms_templates', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('cms_template_sections');

        // Template assets table - tracks template assets
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'template_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'asset_type' => [
                'type' => 'ENUM',
                'constraint' => ['css', 'js', 'image', 'font', 'other'],
            ],
            'file_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'file_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
            ],
            'file_size' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'is_critical' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'load_order' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['template_id', 'asset_type']);
        $this->forge->addForeignKey('template_id', 'cms_templates', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('cms_template_assets');

        // Template settings table - stores active template and global settings
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'setting_key' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'setting_value' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'setting_type' => [
                'type' => 'ENUM',
                'constraint' => ['string', 'integer', 'boolean', 'json'],
                'default' => 'string',
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('setting_key');
        $this->forge->createTable('cms_template_settings');

        // Insert default settings
        $this->db->table('cms_template_settings')->insertBatch([
            [
                'setting_key' => 'active_template',
                'setting_value' => 'space-dynamic',
                'setting_type' => 'string',
                'description' => 'Currently active template slug',
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'template_backup_enabled',
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'description' => 'Enable automatic template backup before switching',
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'setting_key' => 'auto_parse_sections',
                'setting_value' => '1',
                'setting_type' => 'boolean',
                'description' => 'Automatically parse template sections during installation',
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ]);
    }

    public function down()
    {
        $this->forge->dropTable('cms_template_settings');
        $this->forge->dropTable('cms_template_assets');
        $this->forge->dropTable('cms_template_sections');
        $this->forge->dropTable('cms_templates');
    }
}
