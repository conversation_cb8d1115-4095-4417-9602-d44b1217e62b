<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateExistingPagesAndMenus extends Migration
{
    public function up()
    {
        // Update existing pages with full_slug based on current slug
        $this->db->query('UPDATE cms_pages SET full_slug = slug WHERE full_slug IS NULL OR full_slug = ""');

        // Create dynamic routes for all existing published pages
        $pages = $this->db->query('SELECT id, slug, full_slug, status FROM cms_pages WHERE status = "published"')->getResult();
        
        foreach ($pages as $page) {
            $fullSlug = $page->full_slug ?: $page->slug;
            
            // Check if route already exists
            $existingRoute = $this->db->query('SELECT id FROM dynamic_routes WHERE page_id = ?', [$page->id])->getRow();
            
            if (!$existingRoute) {
                $this->db->query('INSERT INTO dynamic_routes (route_pattern, controller_method, page_id, route_type, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())', [
                    $fullSlug,
                    'CmsPage::viewDynamic/' . $page->id,
                    $page->id,
                    'page',
                    'active'
                ]);
            }
        }

        // Auto-link existing menus to pages based on URL matching
        $this->autoLinkExistingMenusToPages();

        // Update hierarchical page structures for service pages
        $this->updateServicePageHierarchy();

        // Update menu URLs to match new page structure
        $this->updateMenuUrls();
    }

    public function down()
    {
        // Remove auto-generated dynamic routes
        $this->db->query('DELETE FROM dynamic_routes WHERE controller_method LIKE "CmsPage::viewDynamic/%"');
        
        // Unlink menus from pages
        $this->db->query('UPDATE cms_menus SET page_id = NULL');
        
        // Reset full_slug to original slug
        $this->db->query('UPDATE cms_pages SET full_slug = slug');
    }

    /**
     * Auto-link existing menus to pages based on URL matching
     */
    private function autoLinkExistingMenusToPages()
    {
        // Get all menus and pages
        $menus = $this->db->query('SELECT id, title, url FROM cms_menus WHERE page_id IS NULL')->getResult();
        $pages = $this->db->query('SELECT id, title, slug, full_slug FROM cms_pages WHERE status = "published"')->getResult();

        foreach ($menus as $menu) {
            $menuUrl = trim($menu->url, '/');
            
            foreach ($pages as $page) {
                $pageSlug = $page->full_slug ?: $page->slug;
                
                // Direct URL match
                if ($menuUrl === $pageSlug || $menuUrl === '/' . $pageSlug) {
                    $this->db->query('UPDATE cms_menus SET page_id = ? WHERE id = ?', [$page->id, $menu->id]);
                    break;
                }
                
                // Title-based matching (fuzzy match)
                if ($this->isSimilarTitle($menu->title, $page->title)) {
                    $this->db->query('UPDATE cms_menus SET page_id = ? WHERE id = ?', [$page->id, $menu->id]);
                    break;
                }
            }
        }
    }

    /**
     * Update service page hierarchy
     */
    private function updateServicePageHierarchy()
    {
        // Find the main services page
        $servicesPage = $this->db->query('SELECT id FROM cms_pages WHERE slug = "services" LIMIT 1')->getRow();
        
        if ($servicesPage) {
            // Update all service sub-pages to have services as parent
            $servicePages = $this->db->query('SELECT id, slug FROM cms_pages WHERE slug LIKE "services/%" AND slug != "services"')->getResult();
            
            foreach ($servicePages as $servicePage) {
                // Extract the service slug (remove "services/" prefix)
                $serviceSlug = str_replace('services/', '', $servicePage->slug);
                
                // Update parent_id and full_slug
                $this->db->query('UPDATE cms_pages SET parent_id = ?, full_slug = ? WHERE id = ?', [
                    $servicesPage->id,
                    'services/' . $serviceSlug,
                    $servicePage->id
                ]);

                // Update dynamic route
                $this->db->query('UPDATE dynamic_routes SET route_pattern = ?, route_type = ? WHERE page_id = ?', [
                    'services/' . $serviceSlug,
                    'hierarchical',
                    $servicePage->id
                ]);
            }
        }

        // Find the main products page and do the same
        $productsPage = $this->db->query('SELECT id FROM cms_pages WHERE slug = "products" LIMIT 1')->getRow();
        
        if ($productsPage) {
            $productPages = $this->db->query('SELECT id, slug FROM cms_pages WHERE slug LIKE "products/%" AND slug != "products"')->getResult();
            
            foreach ($productPages as $productPage) {
                $productSlug = str_replace('products/', '', $productPage->slug);
                
                $this->db->query('UPDATE cms_pages SET parent_id = ?, full_slug = ? WHERE id = ?', [
                    $productsPage->id,
                    'products/' . $productSlug,
                    $productPage->id
                ]);

                $this->db->query('UPDATE dynamic_routes SET route_pattern = ?, route_type = ? WHERE page_id = ?', [
                    'products/' . $productSlug,
                    'hierarchical',
                    $productPage->id
                ]);
            }
        }
    }

    /**
     * Update menu URLs to match new page structure
     */
    private function updateMenuUrls()
    {
        // Update menu URLs based on linked pages
        $linkedMenus = $this->db->query('
            SELECT m.id as menu_id, m.url as current_url, p.full_slug 
            FROM cms_menus m 
            JOIN cms_pages p ON m.page_id = p.id 
            WHERE m.page_id IS NOT NULL
        ')->getResult();

        foreach ($linkedMenus as $menu) {
            $newUrl = '/' . $menu->full_slug;
            if ($menu->current_url !== $newUrl) {
                $this->db->query('UPDATE cms_menus SET url = ? WHERE id = ?', [$newUrl, $menu->menu_id]);
            }
        }
    }

    /**
     * Check if two titles are similar (for auto-linking)
     */
    private function isSimilarTitle($title1, $title2)
    {
        // Normalize titles for comparison
        $normalize = function($title) {
            return strtolower(trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $title)));
        };

        $norm1 = $normalize($title1);
        $norm2 = $normalize($title2);

        // Exact match
        if ($norm1 === $norm2) {
            return true;
        }

        // Check if one title contains the other
        if (strpos($norm1, $norm2) !== false || strpos($norm2, $norm1) !== false) {
            return true;
        }

        // Check for similar words (at least 70% similarity)
        $words1 = explode(' ', $norm1);
        $words2 = explode(' ', $norm2);
        
        $commonWords = array_intersect($words1, $words2);
        $totalWords = array_unique(array_merge($words1, $words2));
        
        $similarity = count($commonWords) / count($totalWords);
        
        return $similarity >= 0.7;
    }
}
