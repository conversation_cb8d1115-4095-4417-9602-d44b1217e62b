<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddTemplateConstraints extends Migration
{
    public function up()
    {
        // Skip this migration - template constraints will be handled by application logic
        // The trigger approach causes MySQL conflicts
        return;
    }

    public function down()
    {
        try {
            $this->db->query("DROP TRIGGER IF EXISTS ensure_single_default_template");
        } catch (\Exception $e) {
            // Ignore if trigger doesn't exist
        }
    }
}
