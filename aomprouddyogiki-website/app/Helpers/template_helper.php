<?php

use App\Services\FrontendTemplateService;

// Ensure esc() function exists
if (!function_exists('esc')) {
    function esc($data, $context = 'html') {
        if (is_string($data)) {
            return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }
}

if (!function_exists('template_service')) {
    /**
     * Get the frontend template service instance
     */
    function template_service()
    {
        static $templateService = null;

        if ($templateService === null) {
            try {
                $templateService = new FrontendTemplateService();
            } catch (Exception $e) {
                log_message('error', 'Failed to create FrontendTemplateService: ' . $e->getMessage());
                return null;
            }
        }

        return $templateService;
    }
}

if (!function_exists('active_template')) {
    /**
     * Get the active template information
     */
    function active_template()
    {
        return template_service()->getActiveTemplate();
    }
}

if (!function_exists('template_css')) {
    /**
     * Get CSS assets HTML for the active template
     */
    function template_css()
    {
        try {
            $service = template_service();
            if ($service) {
                return $service->getCssHtml();
            }
        } catch (Exception $e) {
            log_message('error', 'template_css() error: ' . $e->getMessage());
        }

        // Fallback to hardcoded Space Dynamic CSS
        return '<link rel="stylesheet" href="' . base_url('css/fontawesome.css') . '">' . "\n    " .
               '<link rel="stylesheet" href="' . base_url('css/templatemo-space-dynamic.css') . '">' . "\n    " .
               '<link rel="stylesheet" href="' . base_url('css/animated.css') . '">' . "\n    " .
               '<link rel="stylesheet" href="' . base_url('css/owl.css') . '">';
    }
}

if (!function_exists('template_js')) {
    /**
     * Get JavaScript assets HTML for the active template
     */
    function template_js()
    {
        try {
            $service = template_service();
            if ($service) {
                return $service->getJsHtml();
            }
        } catch (Exception $e) {
            log_message('error', 'template_js() error: ' . $e->getMessage());
        }

        // Fallback to hardcoded Space Dynamic JS
        return '<script src="' . base_url('js/owl-carousel.js') . '"></script>' . "\n    " .
               '<script src="' . base_url('js/animation.js') . '"></script>' . "\n    " .
               '<script src="' . base_url('js/imagesloaded.js') . '"></script>' . "\n    " .
               '<script src="' . base_url('js/templatemo-custom.js') . '"></script>';
    }
}

if (!function_exists('template_asset_url')) {
    /**
     * Get asset URL with fallback
     */
    function template_asset_url($filePath, $fallbackPath = null)
    {
        return template_service()->getAssetUrl($filePath, $fallbackPath);
    }
}

if (!function_exists('template_debug')) {
    /**
     * Get template debug information
     */
    function template_debug()
    {
        return template_service()->getTemplateDebugInfo();
    }
}
