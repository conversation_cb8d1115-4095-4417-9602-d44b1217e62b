<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
// Home routes
$routes->get('/', 'Home::index');
$routes->get('/about', 'Home::about');

// Services routes
$routes->get('/services', 'Services::index');
$routes->get('/services/web-development', 'Services::webDevelopment');
$routes->get('/services/mobile-development', 'Services::mobileDevelopment');
$routes->get('/services/ecommerce-development', 'Services::ecommerceDevelopment');
$routes->get('/services/cms-development', 'Services::cmsDevelopment');
$routes->get('/services/api-development', 'Services::apiDevelopment');
$routes->get('/services/hosting', 'Services::hosting');
$routes->get('/services/digital-marketing', 'Services::digitalMarketing');
$routes->get('/services/maintenance-support', 'Services::maintenanceSupport');

// Products routes
$routes->get('/products', 'Products::index');
$routes->get('/products/govt-exam-prep', 'Products::govtExamPrep');

// Contact routes
$routes->get('/contact', 'Contact::index');
$routes->post('/contact/submit', 'Contact::submit');

// Template test route
$routes->get('/template-test', 'TemplateTest::index');

// Template switcher routes
$routes->get('/template-switcher', 'TemplateSwitcher::index');
$routes->post('/template-switcher/switch', 'TemplateSwitcher::switch');
$routes->get('/template-switcher/refresh', 'TemplateSwitcher::refresh');
$routes->get('/template-switcher/status', 'TemplateSwitcher::status');

// Admin routes
$routes->group('admin', function($routes) {
    // Authentication
    $routes->get('login', 'Admin::login');
    $routes->post('authenticate', 'Admin::authenticate');
    $routes->get('logout', 'Admin::logout');

    // Dashboard
    $routes->get('dashboard', 'Admin::dashboard');
    $routes->get('/', 'Admin::dashboard');

    // Pages management
    $routes->get('pages', 'CmsPages::index');
    $routes->get('pages/create', 'CmsPages::create');
    $routes->post('pages/create', 'CmsPages::store');
    $routes->get('pages/edit/(:num)', 'CmsPages::edit/$1');
    $routes->post('pages/edit/(:num)', 'CmsPages::update/$1');
    $routes->get('pages/delete/(:num)', 'CmsPages::delete/$1');
    $routes->get('pages/preview/(:num)', 'CmsPages::preview/$1');

    // Menus management
    $routes->get('menus', 'CmsMenus::index');
    $routes->get('menus/create', 'CmsMenus::create');
    $routes->post('menus/create', 'CmsMenus::store');
    $routes->get('menus/edit/(:num)', 'CmsMenus::edit/$1');
    $routes->post('menus/edit/(:num)', 'CmsMenus::update/$1');
    $routes->get('menus/delete/(:num)', 'CmsMenus::delete/$1');

    // Page-Menu Associations
    $routes->get('page-menu-associations', 'CmsPageMenuAssociations::index');
    $routes->get('page-menu-associations/auto-link', 'CmsPageMenuAssociations::autoLink');
    $routes->post('page-menu-associations/link-page-to-menu', 'CmsPageMenuAssociations::linkPageToMenu');
    $routes->get('page-menu-associations/unlink/(:num)', 'CmsPageMenuAssociations::unlinkPageFromMenu/$1');
    $routes->post('page-menu-associations/create-menu-for-page', 'CmsPageMenuAssociations::createMenuForPage');
    $routes->get('page-menu-associations/generate-all-routes', 'CmsPageMenuAssociations::generateAllRoutes');
    $routes->post('menus/update-order', 'CmsMenus::updateOrder');

    // Content blocks management
    $routes->get('content-blocks', 'CmsContentBlocks::index');
    $routes->get('content-blocks/create', 'CmsContentBlocks::create');
    $routes->post('content-blocks/create', 'CmsContentBlocks::store');
    $routes->get('content-blocks/edit/(:num)', 'CmsContentBlocks::edit/$1');
    $routes->post('content-blocks/edit/(:num)', 'CmsContentBlocks::update/$1');
    $routes->get('content-blocks/delete/(:num)', 'CmsContentBlocks::delete/$1');
    $routes->post('content-blocks/update-order', 'CmsContentBlocks::updateOrder');

    // Image management
    $routes->get('images/browser', 'ImageUpload::browser');
    $routes->post('images/upload', 'ImageUpload::upload');
    $routes->get('images/browse', 'ImageUpload::browse');
    $routes->post('images/delete', 'ImageUpload::delete');

    // Template Engine routes
    $routes->get('templates', 'CmsTemplates::index');
    $routes->get('templates/install', 'CmsTemplates::install');
    $routes->post('templates/install', 'CmsTemplates::install');
    $routes->get('templates/view/(:num)', 'CmsTemplates::view/$1');
    $routes->post('templates/switch', 'CmsTemplates::switch');
    $routes->get('templates/delete/(:num)', 'CmsTemplates::delete/$1');
    $routes->get('templates/sections/(:num)', 'CmsTemplates::sections/$1');
    $routes->get('templates/assets/(:num)', 'CmsTemplates::assets/$1');
    $routes->get('templates/settings', 'CmsTemplates::settings');
    $routes->post('templates/settings', 'CmsTemplates::settings');
    $routes->get('templates/search', 'CmsTemplates::search');
    $routes->get('templates/debug', 'CmsTemplates::debug');
    $routes->get('templates/quick-switch/(:num)', 'CmsTemplates::quickSwitch/$1');
});

// Register dynamic routes from database
try {
    $routeService = new \App\Services\DynamicRouteService();
    $routeService->registerDynamicRoutes($routes);
} catch (Exception $e) {
    // Silently fail during migration or when database is not ready
}

// CMS dynamic pages routes - these should be before the catch-all route
$routes->get('services/(:segment)', 'CmsPage::viewBySlug/services/$1');
$routes->get('products/(:segment)', 'CmsPage::viewBySlug/products/$1');

// CMS dynamic pages route (should be last to avoid conflicts)
$routes->get('cms/(:segment)', 'CmsPage::view/$1');

// Hierarchical page routes (supports unlimited nesting)
$routes->get('(:segment)/(:segment)', 'CmsPage::viewByFullSlug/$1/$2');
$routes->get('(:segment)/(:segment)/(:segment)', 'CmsPage::viewByFullSlug/$1/$2/$3');
$routes->get('(:segment)/(:segment)/(:segment)/(:segment)', 'CmsPage::viewByFullSlug/$1/$2/$3/$4');

// Catch-all route for CMS pages (should be last)
$routes->get('(:segment)', 'CmsPage::viewBySlug/$1');

// API routes for future mobile app integration
$routes->group('api', ['namespace' => 'App\Controllers\Api'], function($routes) {
    $routes->get('services', 'ApiServices::index');
    $routes->get('products', 'ApiProducts::index');
    $routes->post('contact', 'ApiContact::submit');
    $routes->get('company-info', 'ApiCompany::info');
});
