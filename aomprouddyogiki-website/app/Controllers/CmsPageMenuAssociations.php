<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\CmsPageModel;
use App\Models\CmsMenuModel;
use App\Models\CmsAdminModel;
use App\Services\DynamicRouteService;

class CmsPageMenuAssociations extends BaseController
{
    protected $pageModel;
    protected $menuModel;
    protected $adminModel;
    protected $routeService;
    protected $session;

    public function __construct()
    {
        $this->pageModel = new CmsPageModel();
        $this->menuModel = new CmsMenuModel();
        $this->adminModel = new CmsAdminModel();
        $this->routeService = new DynamicRouteService();
        $this->session = \Config\Services::session();
    }

    /**
     * Check admin authentication
     */
    private function checkAuth()
    {
        if (!$this->session->get('admin_logged_in')) {
            return redirect()->to('/admin/login');
        }
        return null;
    }

    /**
     * Get admin data for views
     */
    private function getAdminData()
    {
        $adminId = $this->session->get('admin_id');
        return $this->adminModel->find($adminId);
    }

    /**
     * Display page-menu associations management
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        // Get all pages with their menu associations
        $pages = $this->pageModel->getPagesWithMenus();
        
        // Get unlinked menus
        $unlinkedMenus = $this->menuModel->getUnlinkedMenus('primary');
        
        // Get association statistics
        $stats = [
            'total_pages' => $this->pageModel->countAll(),
            'linked_pages' => count(array_filter($pages, function($page) { return !empty($page['menu_id']); })),
            'unlinked_pages' => count(array_filter($pages, function($page) { return empty($page['menu_id']); })),
            'total_menus' => $this->menuModel->countAll(),
            'linked_menus' => $this->menuModel->where('page_id IS NOT NULL')->countAllResults(false),
            'unlinked_menus' => count($unlinkedMenus)
        ];

        $data = [
            'title' => 'Page-Menu Associations - AomProuddyogiki CMS',
            'meta_description' => 'Manage page and menu associations',
            'pages' => $pages,
            'unlinkedMenus' => $unlinkedMenus,
            'stats' => $stats,
            'admin' => $this->getAdminData()
        ];

        return view('admin/page_menu_associations/index', $data);
    }

    /**
     * Auto-link pages and menus based on intelligent matching
     */
    public function autoLink()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $linkedCount = 0;
        $routesCreated = 0;
        $errors = [];

        try {
            // Get unlinked pages and menus
            $unlinkedPages = $this->pageModel->select('cms_pages.*')
                                            ->join('cms_menus', 'cms_menus.page_id = cms_pages.id', 'left')
                                            ->where('cms_menus.page_id IS NULL')
                                            ->where('cms_pages.status', 'published')
                                            ->findAll();

            $unlinkedMenus = $this->menuModel->getUnlinkedMenus('primary');

            foreach ($unlinkedPages as $page) {
                try {
                    foreach ($unlinkedMenus as $menu) {
                        if ($this->shouldLinkPageToMenu($page, $menu)) {
                            // Link the page to menu
                            $this->menuModel->linkToPage($menu['id'], $page['id']);

                            // Update menu URL
                            $fullSlug = $page['full_slug'] ?: $page['slug'];
                            $this->menuModel->update($menu['id'], ['url' => '/' . $fullSlug]);

                            $linkedCount++;

                            // Remove this menu from available menus
                            $unlinkedMenus = array_filter($unlinkedMenus, function($m) use ($menu) {
                                return $m['id'] !== $menu['id'];
                            });

                            break; // Move to next page
                        }
                    }

                    // Create dynamic route if not exists
                    if ($this->routeService->generatePageRoute($page['id'], $page['slug'], $page['parent_id'])) {
                        $routesCreated++;
                    }
                } catch (Exception $e) {
                    $errors[] = "Error processing page '{$page['title']}': " . $e->getMessage();
                    log_message('error', 'Auto-link error for page ' . $page['id'] . ': ' . $e->getMessage());
                }
            }

            $message = "Auto-linking completed! {$linkedCount} associations created, {$routesCreated} routes generated.";
            if (!empty($errors)) {
                $message .= " Some errors occurred: " . implode(', ', array_slice($errors, 0, 3));
                if (count($errors) > 3) {
                    $message .= " and " . (count($errors) - 3) . " more.";
                }
            }

            return redirect()->to('/admin/page-menu-associations')->with('success', $message);
        } catch (Exception $e) {
            log_message('error', 'Auto-link process failed: ' . $e->getMessage());
            return redirect()->to('/admin/page-menu-associations')
                            ->with('error', 'Auto-linking failed. Please check the logs for details.');
        }
    }

    /**
     * Link a specific page to a specific menu
     */
    public function linkPageToMenu()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $pageId = $this->request->getPost('page_id');
        $menuId = $this->request->getPost('menu_id');

        if (!$pageId || !$menuId) {
            return redirect()->back()->with('error', 'Page ID and Menu ID are required.');
        }

        $page = $this->pageModel->find($pageId);
        $menu = $this->menuModel->find($menuId);

        if (!$page || !$menu) {
            return redirect()->back()->with('error', 'Page or Menu not found.');
        }

        // Check if menu is already linked
        if ($menu['page_id']) {
            return redirect()->back()->with('error', 'Menu is already linked to another page.');
        }

        try {
            // Link the page to menu
            $this->menuModel->linkToPage($menuId, $pageId);

            // Update menu URL
            $fullSlug = $page['full_slug'] ?: $page['slug'];
            $this->menuModel->update($menuId, ['url' => '/' . $fullSlug]);

            return redirect()->back()->with('success', 'Page linked to menu successfully.');
        } catch (Exception $e) {
            log_message('error', 'Failed to link page to menu: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to link page to menu. Please try again.');
        }
    }

    /**
     * Unlink a page from its menu
     */
    public function unlinkPageFromMenu($pageId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $page = $this->pageModel->find($pageId);
        if (!$page) {
            return redirect()->back()->with('error', 'Page not found.');
        }

        // Get associated menus
        $menus = $this->menuModel->getByPageId($pageId);
        
        // Unlink all associated menus
        foreach ($menus as $menu) {
            $this->menuModel->unlinkFromPage($menu['id']);
        }

        return redirect()->back()->with('success', 'Page unlinked from menu(s) successfully.');
    }

    /**
     * Create menu for page
     */
    public function createMenuForPage()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $pageId = $this->request->getPost('page_id');
        $menuLocation = $this->request->getPost('menu_location') ?: 'primary';
        $menuParentId = $this->request->getPost('menu_parent_id') ?: null;

        $page = $this->pageModel->find($pageId);
        if (!$page) {
            return redirect()->back()->with('error', 'Page not found.');
        }

        // Create menu item for page
        $page['full_slug'] = $page['full_slug'] ?: $page['slug'];
        $menuId = $this->menuModel->createForPage($page, $menuLocation, $menuParentId);

        if ($menuId) {
            return redirect()->back()->with('success', 'Menu item created and linked to page successfully.');
        }

        return redirect()->back()->with('error', 'Failed to create menu item.');
    }

    /**
     * Generate routes for all pages
     */
    public function generateAllRoutes()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $pages = $this->pageModel->where('status', 'published')->findAll();
        $routesCreated = 0;
        $routesUpdated = 0;

        foreach ($pages as $page) {
            $result = $this->routeService->generatePageRoute($page['id'], $page['slug'], $page['parent_id']);
            if ($result) {
                if (strpos($result, 'updated') !== false) {
                    $routesUpdated++;
                } else {
                    $routesCreated++;
                }
            }
        }

        return redirect()->back()->with('success', "Route generation completed! {$routesCreated} routes created, {$routesUpdated} routes updated.");
    }

    /**
     * Determine if a page should be linked to a menu based on intelligent matching
     */
    private function shouldLinkPageToMenu($page, $menu)
    {
        // Direct URL match
        $pageSlug = $page['full_slug'] ?: $page['slug'];
        $menuUrl = trim($menu['url'], '/');
        
        if ($menuUrl === $pageSlug || $menuUrl === '/' . $pageSlug) {
            return true;
        }

        // Title similarity match
        return $this->isSimilarTitle($page['title'], $menu['title']);
    }

    /**
     * Check if two titles are similar
     */
    private function isSimilarTitle($title1, $title2)
    {
        // Normalize titles for comparison
        $normalize = function($title) {
            return strtolower(trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $title)));
        };

        $norm1 = $normalize($title1);
        $norm2 = $normalize($title2);

        // Exact match
        if ($norm1 === $norm2) {
            return true;
        }

        // Check if one title contains the other
        if (strpos($norm1, $norm2) !== false || strpos($norm2, $norm1) !== false) {
            return true;
        }

        // Check for similar words (at least 70% similarity)
        $words1 = explode(' ', $norm1);
        $words2 = explode(' ', $norm2);
        
        $commonWords = array_intersect($words1, $words2);
        $totalWords = array_unique(array_merge($words1, $words2));
        
        if (count($totalWords) === 0) return false;
        
        $similarity = count($commonWords) / count($totalWords);
        
        return $similarity >= 0.7;
    }
}
