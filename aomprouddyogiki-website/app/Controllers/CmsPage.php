<?php

namespace App\Controllers;

use App\Models\CmsPageModel;
use App\Models\CmsContentBlockModel;
use App\Models\CmsMenuModel;
use App\Services\DynamicRouteService;

class CmsPage extends BaseController
{
    protected $pageModel;
    protected $contentBlockModel;
    protected $menuModel;
    protected $routeService;

    public function __construct()
    {
        $this->pageModel = new CmsPageModel();
        $this->contentBlockModel = new CmsContentBlockModel();
        $this->menuModel = new CmsMenuModel();
        $this->routeService = new DynamicRouteService();
    }

    /**
     * Display CMS page by slug
     */
    public function view($slug)
    {
        $page = $this->pageModel->getBySlug($slug);

        if (!$page) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        return $this->renderPage($page);
    }

    /**
     * Display CMS page by slug with optional prefix
     */
    public function viewBySlug($prefix = null, $slug = null)
    {
        // If only one parameter, it's the slug
        if ($slug === null) {
            $fullSlug = $prefix;
        } else {
            $fullSlug = $prefix . '/' . $slug;
        }

        $page = $this->pageModel->getBySlug($fullSlug);

        if (!$page) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        return $this->renderPage($page);
    }

    /**
     * Render page with common data
     */
    private function renderPage($page)
    {
        // Get content blocks for this page
        $contentBlocks = $this->contentBlockModel->getByPageId($page['id']);

        // Get navigation menu
        $primaryMenu = $this->menuModel->getHierarchicalMenu('primary');

        $data = [
            'title' => $page['meta_title'] ?: $page['title'],
            'meta_description' => $page['meta_description'] ?: $page['excerpt'],
            'meta_keywords' => $page['meta_keywords'],
            'page' => $page,
            'contentBlocks' => $contentBlocks,
            'primaryMenu' => $primaryMenu
        ];

        // Choose template based on page template setting
        $template = $this->getTemplate($page['template']);

        return view($template, $data);
    }

    /**
     * Get template file based on page template type
     */
    private function getTemplate($templateType)
    {
        $templates = [
            'default' => 'cms/templates/default',
            'service' => 'cms/templates/service',
            'product' => 'cms/templates/product',
            'about' => 'cms/templates/about',
            'contact' => 'cms/templates/contact'
        ];

        return $templates[$templateType] ?? $templates['default'];
    }

    /**
     * Get all published pages for sitemap or navigation
     */
    public function getAllPages()
    {
        return $this->pageModel->getPublishedPages();
    }

    /**
     * Search pages
     */
    public function search()
    {
        $keyword = $this->request->getGet('q');
        
        if (empty($keyword)) {
            return redirect()->to('/');
        }

        $pages = $this->pageModel->searchPages($keyword);
        $primaryMenu = $this->menuModel->getHierarchicalMenu('primary');

        $data = [
            'title' => 'Search Results for: ' . esc($keyword),
            'meta_description' => 'Search results for ' . esc($keyword),
            'keyword' => $keyword,
            'pages' => $pages,
            'primaryMenu' => $primaryMenu
        ];

        return view('cms/search_results', $data);
    }

    /**
     * Dynamic page view by ID (for dynamic routing)
     */
    public function viewDynamic($pageId)
    {
        $page = $this->pageModel->find($pageId);

        if (!$page || $page['status'] !== 'published') {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        // Get content blocks for this page
        $contentBlocks = $this->contentBlockModel->getByPageId($pageId);

        // Get primary menu
        $primaryMenu = $this->menuModel->getHierarchicalMenu('primary');

        // Get page hierarchy for breadcrumbs
        $hierarchy = $this->routeService->getPageHierarchy($pageId);

        $data = [
            'title' => $page['meta_title'] ?: $page['title'],
            'meta_description' => $page['meta_description'] ?: $page['excerpt'],
            'meta_keywords' => $page['meta_keywords'],
            'page' => $page,
            'contentBlocks' => $contentBlocks,
            'primaryMenu' => $primaryMenu,
            'hierarchy' => $hierarchy
        ];

        // Determine template
        $template = $page['template'] ?: 'default';
        $templatePath = "cms/templates/{$template}";

        // Check if template exists, fallback to default
        if (!view_exists($templatePath)) {
            $templatePath = 'cms/templates/default';
        }

        return view($templatePath, $data);
    }

    /**
     * View page by full slug (enhanced for hierarchical URLs)
     */
    public function viewByFullSlug(...$segments)
    {
        if (empty($segments)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        // Build full slug from segments
        $fullSlug = implode('/', $segments);

        // Try to find page by full slug first
        $page = $this->pageModel->getByFullSlug($fullSlug);

        if (!$page) {
            // Fallback to regular slug for backward compatibility
            $page = $this->pageModel->getBySlug($fullSlug);
        }

        if (!$page) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        return $this->viewDynamic($page['id']);
    }

    /**
     * Handle hierarchical page routing (legacy method)
     */
    public function viewHierarchical(...$segments)
    {
        return $this->viewByFullSlug(...$segments);
    }
}
