<?php

namespace App\Controllers;

use App\Models\CmsTemplateModel;
use App\Models\CmsTemplateSettingModel;

class TemplateSwitcher extends BaseController
{
    protected $templateModel;
    protected $settingModel;

    public function __construct()
    {
        $this->templateModel = new CmsTemplateModel();
        $this->settingModel = new CmsTemplateSettingModel();
    }

    public function index()
    {
        try {
            $data = [
                'title' => 'Template Switcher',
                'meta_description' => 'Switch between available templates',
                'templates' => $this->templateModel->findAll(),
                'active_template' => $this->settingModel->getActiveTemplate(),
                'template_settings' => $this->settingModel->getTemplateEngineConfig(),
            ];
            
            return view('template_switcher', $data);
            
        } catch (\Exception $e) {
            return "Error: " . $e->getMessage() . "<br>Stack trace:<br>" . nl2br($e->getTraceAsString());
        }
    }

    public function switch()
    {
        $templateSlug = $this->request->getPost('template_slug');

        if (!$templateSlug) {
            return redirect()->back()->with('error', 'No template selected');
        }

        try {
            // Check if template exists
            $template = $this->templateModel->where('slug', $templateSlug)->first();
            if (!$template) {
                return redirect()->back()->with('error', 'Template not found');
            }

            // Use database transaction for atomic operation
            $db = \Config\Database::connect();
            $db->transStart();

            // Update all templates to not be default (using raw query for safety)
            $db->query("UPDATE cms_templates SET is_default = 0 WHERE is_default = 1");

            // Set selected template as default
            $db->query("UPDATE cms_templates SET is_default = 1 WHERE slug = ?", [$templateSlug]);

            // Update setting
            $this->settingModel->setActiveTemplate($templateSlug);

            $db->transComplete();

            if ($db->transStatus() === false) {
                return redirect()->back()->with('error', 'Database transaction failed');
            }

            // Clear template cache to force reload
            \App\Libraries\TemplateLoader::clearCache();
            cache()->clean();

            return redirect()->back()->with('success', 'Template switched successfully to: ' . $template['name']);

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error switching template: ' . $e->getMessage());
        }
    }

    public function refresh()
    {
        try {
            // Clear any template cache if exists
            cache()->clean();

            // Also clear any view cache
            if (function_exists('opcache_reset')) {
                opcache_reset();
            }

            return redirect()->back()->with('success', 'Template cache refreshed successfully');

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error refreshing cache: ' . $e->getMessage());
        }
    }

    public function status()
    {
        try {
            $data = [
                'title' => 'Template System Status',
                'meta_description' => 'Template system status and diagnostics',
                'active_template' => $this->settingModel->getActiveTemplate(),
                'template_loader_test' => $this->testTemplateLoader(),
                'database_status' => $this->checkDatabaseStatus(),
                'file_status' => $this->checkFileStatus(),
            ];

            return view('template_status', $data);

        } catch (\Exception $e) {
            return "Error: " . $e->getMessage() . "<br>Stack trace:<br>" . nl2br($e->getTraceAsString());
        }
    }

    private function testTemplateLoader()
    {
        try {
            $template = \App\Libraries\TemplateLoader::getActiveTemplate();
            $cssAssets = \App\Libraries\TemplateLoader::getCssAssets();
            $jsAssets = \App\Libraries\TemplateLoader::getJsAssets();

            return [
                'status' => 'success',
                'template' => $template,
                'css_count' => count($cssAssets),
                'js_count' => count($jsAssets),
                'message' => 'TemplateLoader working correctly'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkDatabaseStatus()
    {
        try {
            $templateCount = $this->templateModel->countAll();
            $activeTemplates = $this->templateModel->where('is_default', 1)->countAllResults();
            $settingCount = $this->settingModel->countAll();

            return [
                'status' => 'success',
                'template_count' => $templateCount,
                'active_templates' => $activeTemplates,
                'setting_count' => $settingCount,
                'message' => 'Database connections working'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkFileStatus()
    {
        $publicPath = FCPATH;
        $cssPath = $publicPath . 'css/';
        $jsPath = $publicPath . 'js/';

        $cssFiles = ['fontawesome.css', 'templatemo-space-dynamic.css', 'animated.css', 'owl.css'];
        $jsFiles = ['isotope.js', 'owl-carousel.js', 'animation.js', 'templatemo-custom.js'];

        $status = [
            'css_files' => [],
            'js_files' => [],
            'css_missing' => 0,
            'js_missing' => 0
        ];

        foreach ($cssFiles as $file) {
            $exists = file_exists($cssPath . $file);
            $status['css_files'][$file] = $exists;
            if (!$exists) $status['css_missing']++;
        }

        foreach ($jsFiles as $file) {
            $exists = file_exists($jsPath . $file);
            $status['js_files'][$file] = $exists;
            if (!$exists) $status['js_missing']++;
        }

        return $status;
    }
}
