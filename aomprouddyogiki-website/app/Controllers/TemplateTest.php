<?php

namespace App\Controllers;

class TemplateTest extends BaseController
{
    public function index()
    {
        try {
            $data = [
                'title' => 'Template Loader Test',
                'meta_description' => 'Testing the template loader functionality',
                'template_info' => \App\Libraries\TemplateLoader::getActiveTemplate(),
                'css_assets' => \App\Libraries\TemplateLoader::getCssAssets(),
                'js_assets' => \App\Libraries\TemplateLoader::getJsAssets(),
                'css_html' => \App\Libraries\TemplateLoader::renderCss(),
                'js_html' => \App\Libraries\TemplateLoader::renderJs(),
            ];
            
            return view('template_test', $data);
            
        } catch (\Exception $e) {
            return "Error: " . $e->getMessage() . "<br>Stack trace:<br>" . nl2br($e->getTraceAsString());
        }
    }
}
