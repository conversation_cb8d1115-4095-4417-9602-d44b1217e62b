<?php

namespace App\Controllers;

use App\Models\CmsPageModel;
use App\Models\CmsContentBlockModel;
use App\Models\CmsAdminModel;
use App\Models\CmsMenuModel;
use App\Services\DynamicRouteService;

class CmsPages extends BaseController
{
    protected $pageModel;
    protected $contentBlockModel;
    protected $adminModel;
    protected $menuModel;
    protected $routeService;
    protected $session;

    public function __construct()
    {
        $this->pageModel = new CmsPageModel();
        $this->contentBlockModel = new CmsContentBlockModel();
        $this->adminModel = new CmsAdminModel();
        $this->menuModel = new CmsMenuModel();
        $this->routeService = new DynamicRouteService();
        $this->session = \Config\Services::session();
    }

    /**
     * Pages list
     */
    public function index()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $pages = $this->pageModel->getPagesWithMenus();

        $data = [
            'title' => 'Manage Pages - AomProuddyogiki CMS',
            'meta_description' => 'Manage website pages',
            'pages' => $pages,
            'admin' => $this->getAdminData()
        ];

        return view('admin/pages/index', $data);
    }

    /**
     * Create new page
     */
    public function create()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        if ($this->request->getMethod() === 'POST') {
            return $this->store();
        }

        // Get parent pages for hierarchical structure
        $parentPages = $this->pageModel->getParentPages();

        // Get existing menus for linking
        $existingMenus = $this->menuModel->getUnlinkedMenus('primary');

        $data = [
            'title' => 'Create New Page - AomProuddyogiki CMS',
            'meta_description' => 'Create a new website page',
            'parentPages' => $parentPages,
            'existingMenus' => $existingMenus,
            'admin' => $this->getAdminData()
        ];

        return view('admin/pages/create', $data);
    }

    /**
     * Store new page
     */
    public function store()
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $parentId = $this->request->getPost('parent_id') ?: null;
        $slug = $this->request->getPost('slug');

        // Generate unique slug if not provided
        if (empty($slug)) {
            $slug = $this->routeService->generateUniqueSlug($this->request->getPost('title'), $parentId);
        }

        $data = [
            'title' => $this->request->getPost('title'),
            'slug' => $slug,
            'parent_id' => $parentId,
            'content' => $this->request->getPost('content'),
            'excerpt' => $this->request->getPost('excerpt'),
            'meta_title' => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords' => $this->request->getPost('meta_keywords'),
            'template' => $this->request->getPost('template'),
            'hero_image' => $this->request->getPost('hero_image'),
            'hero_title' => $this->request->getPost('hero_title'),
            'hero_subtitle' => $this->request->getPost('hero_subtitle'),
            'hero_cta_text' => $this->request->getPost('hero_cta_text'),
            'hero_cta_link' => $this->request->getPost('hero_cta_link'),
            'status' => $this->request->getPost('status'),
            'sort_order' => $this->request->getPost('sort_order') ?: 0,
            'author_id' => $this->session->get('admin_id')
        ];

        $pageId = $this->pageModel->insert($data);

        if ($pageId) {
            // Generate dynamic route
            $fullSlug = $this->routeService->generatePageRoute($pageId, $slug, $parentId);

            // Handle menu association
            $menuAction = $this->request->getPost('menu_action');
            $existingMenuId = $this->request->getPost('existing_menu_id');
            $createNewMenu = $this->request->getPost('create_new_menu');
            $menuLocation = $this->request->getPost('menu_location') ?: 'primary';
            $menuParentId = $this->request->getPost('menu_parent_id') ?: null;

            if ($menuAction === 'link_existing' && $existingMenuId) {
                // Link to existing menu
                $this->menuModel->linkToPage($existingMenuId, $pageId);
                $this->menuModel->update($existingMenuId, ['url' => '/' . $fullSlug]);
            } elseif ($menuAction === 'create_new' && $createNewMenu) {
                // Create new menu item
                $pageData = $this->pageModel->find($pageId);
                $pageData['full_slug'] = $fullSlug;
                $this->menuModel->createForPage($pageData, $menuLocation, $menuParentId);
            }

            return redirect()->to('/admin/pages')->with('success', 'Page created successfully with dynamic routing');
        }

        $errors = $this->pageModel->errors();
        return redirect()->back()->withInput()->with('errors', $errors);
    }

    /**
     * Edit page
     */
    public function edit($id)
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $page = $this->pageModel->find($id);
        if (!$page) {
            return redirect()->to('/admin/pages')->with('error', 'Page not found');
        }

        if ($this->request->getMethod() === 'POST') {
            return $this->update($id);
        }

        $contentBlocks = $this->contentBlockModel->getByPageId($id);

        // Get parent pages for hierarchical structure
        $parentPages = $this->pageModel->getParentPages();

        // Get existing menus for linking
        $existingMenus = $this->menuModel->getUnlinkedMenus('primary');

        // Get current menu associations
        $associatedMenus = $this->menuModel->getByPageId($id);

        $data = [
            'title' => 'Edit Page - AomProuddyogiki CMS',
            'meta_description' => 'Edit website page',
            'page' => $page,
            'contentBlocks' => $contentBlocks,
            'parentPages' => $parentPages,
            'existingMenus' => $existingMenus,
            'associatedMenus' => $associatedMenus,
            'admin' => $this->getAdminData()
        ];

        return view('admin/pages/edit', $data);
    }

    /**
     * Update page
     */
    public function update($id)
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $page = $this->pageModel->find($id);
        if (!$page) {
            return redirect()->to('/admin/pages')->with('error', 'Page not found');
        }

        $parentId = $this->request->getPost('parent_id') ?: null;
        $newSlug = $this->request->getPost('slug');
        $oldSlug = $page['slug'];
        $oldParentId = $page['parent_id'];

        // Check if slug needs to be unique
        if ($newSlug !== $oldSlug || $parentId !== $oldParentId) {
            if (empty($newSlug)) {
                $newSlug = $this->routeService->generateUniqueSlug($this->request->getPost('title'), $parentId, $id);
            } elseif ($this->routeService->hasRouteConflict($this->routeService->generateFullSlug($newSlug, $parentId), $id)) {
                return redirect()->back()->withInput()->with('error', 'Slug already exists. Please choose a different slug.');
            }
        }

        $data = [
            'title' => $this->request->getPost('title'),
            'slug' => $newSlug,
            'parent_id' => $parentId,
            'content' => $this->request->getPost('content'),
            'excerpt' => $this->request->getPost('excerpt'),
            'meta_title' => $this->request->getPost('meta_title'),
            'meta_description' => $this->request->getPost('meta_description'),
            'meta_keywords' => $this->request->getPost('meta_keywords'),
            'template' => $this->request->getPost('template'),
            'hero_image' => $this->request->getPost('hero_image'),
            'hero_title' => $this->request->getPost('hero_title'),
            'hero_subtitle' => $this->request->getPost('hero_subtitle'),
            'hero_cta_text' => $this->request->getPost('hero_cta_text'),
            'hero_cta_link' => $this->request->getPost('hero_cta_link'),
            'status' => $this->request->getPost('status'),
            'sort_order' => $this->request->getPost('sort_order') ?: 0
        ];

        if ($this->pageModel->update($id, $data)) {
            // Update dynamic route if slug or parent changed
            if ($newSlug !== $oldSlug || $parentId !== $oldParentId) {
                $fullSlug = $this->routeService->generatePageRoute($id, $newSlug, $parentId);

                // Update associated menu URLs
                $this->menuModel->updatePageMenuUrls($id, $fullSlug);

                // Update child page routes if this page has children
                if ($this->pageModel->hasChildren($id)) {
                    $this->routeService->updateChildRoutes($id);
                }
            }

            return redirect()->to('/admin/pages')->with('success', 'Page updated successfully');
        }

        $errors = $this->pageModel->errors();
        return redirect()->back()->withInput()->with('errors', $errors);
    }

    /**
     * Delete page
     */
    public function delete($id)
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $page = $this->pageModel->find($id);
        if (!$page) {
            return redirect()->to('/admin/pages')->with('error', 'Page not found');
        }

        // Check if user wants to delete associated menu items
        $deleteMenus = $this->request->getGet('delete_menus') === '1';

        // Get associated menus before deletion
        $associatedMenus = $this->menuModel->getByPageId($id);

        // Delete associated content blocks
        $this->contentBlockModel->where('page_id', $id)->delete();

        // Remove dynamic routes
        $this->routeService->removePageRoute($id);

        if ($this->pageModel->delete($id)) {
            // Handle menu deletion/unlinking
            if ($deleteMenus) {
                // Delete associated menu items
                foreach ($associatedMenus as $menu) {
                    $this->menuModel->delete($menu['id']);
                }
                $message = 'Page and associated menu items deleted successfully';
            } else {
                // Unlink menus from page
                foreach ($associatedMenus as $menu) {
                    $this->menuModel->unlinkFromPage($menu['id']);
                }
                $message = 'Page deleted successfully. Associated menu items have been unlinked.';
            }

            return redirect()->to('/admin/pages')->with('success', $message);
        }

        return redirect()->to('/admin/pages')->with('error', 'Failed to delete page');
    }

    /**
     * Preview page
     */
    public function preview($id)
    {
        if (!$this->isLoggedIn()) {
            return redirect()->to('/admin/login');
        }

        $page = $this->pageModel->find($id);
        if (!$page) {
            return redirect()->to('/admin/pages')->with('error', 'Page not found');
        }

        $contentBlocks = $this->contentBlockModel->getByPageId($id);

        $data = [
            'title' => $page['title'],
            'meta_description' => $page['meta_description'] ?: $page['excerpt'],
            'page' => $page,
            'contentBlocks' => $contentBlocks,
            'preview_mode' => true
        ];

        return view('cms/page_preview', $data);
    }

    /**
     * Check if admin is logged in
     */
    private function isLoggedIn()
    {
        return $this->session->get('admin_logged_in') === true;
    }

    /**
     * Get current admin data
     */
    private function getAdminData()
    {
        if (!$this->isLoggedIn()) {
            return null;
        }

        return [
            'id' => $this->session->get('admin_id'),
            'username' => $this->session->get('admin_username'),
            'email' => $this->session->get('admin_email'),
            'full_name' => $this->session->get('admin_full_name'),
            'role' => $this->session->get('admin_role')
        ];
    }
}
