/* Creative Agency Theme Overrides */

/* Import base Space Dynamic styles */
@import url('templatemo-space-dynamic.css');

/* Creative Agency Color Scheme - Red/Teal */
:root {
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --accent-color: #45b7d1;
    --text-color: #2c3e50;
    --bg-color: #f8f9fa;
}

/* Override main banner */
.main-banner {
    background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%) !important;
}

/* Creative Agency specific styling */
body.template-creative-agency {
    font-family: 'Poppins', 'Arial', sans-serif;
}

.template-creative-agency .main-banner h2 {
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
}

.template-creative-agency .main-banner p {
    color: #fff3e0;
}

/* Header styling */
.template-creative-agency .header-area {
    background: rgba(255, 107, 107, 0.95) !important;
    backdrop-filter: blur(10px);
}

.template-creative-agency .header-area .nav-link {
    color: #ffffff !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.template-creative-agency .header-area .nav-link:hover {
    color: #fff3e0 !important;
    transform: translateY(-2px);
}

/* Creative button styling */
.template-creative-agency .main-button a {
    background: linear-gradient(45deg, #ff6b6b, #ff5252);
    border: 2px solid #ff6b6b;
    color: white;
    padding: 15px 35px;
    border-radius: 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.template-creative-agency .main-button a:hover {
    background: linear-gradient(45deg, #4ecdc4, #26a69a);
    border-color: #4ecdc4;
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* Creative services section */
.template-creative-agency .services .service-item {
    border: 3px solid transparent;
    border-radius: 20px;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #ff6b6b, #4ecdc4) border-box;
    transition: all 0.4s ease;
}

.template-creative-agency .services .service-item:hover {
    transform: translateY(-10px) rotate(2deg);
    box-shadow: 0 15px 35px rgba(255, 107, 107, 0.2);
}

/* Creative about section */
.template-creative-agency .about-us {
    background: linear-gradient(135deg, #fff5f5 0%, #f0fdfa 100%);
}

/* Animated elements */
.template-creative-agency .service-item h4 {
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.template-creative-agency .service-item:hover h4 {
    color: var(--secondary-color);
    transform: scale(1.1);
}

/* Footer */
.template-creative-agency .footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* Template indicator for Creative Agency */
.template-creative-agency .template-indicator {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Creative card styling */
.template-creative-agency .card {
    border: none;
    border-radius: 20px;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    overflow: hidden;
}

.template-creative-agency .card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.15);
}

/* Creative typography */
.template-creative-agency h1,
.template-creative-agency h2,
.template-creative-agency h3 {
    color: var(--text-color);
    font-weight: 700;
}

.template-creative-agency .section-heading h2 {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.template-creative-agency .section-heading h2::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 2px;
}

/* Creative animations */
.template-creative-agency .service-item,
.template-creative-agency .card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
