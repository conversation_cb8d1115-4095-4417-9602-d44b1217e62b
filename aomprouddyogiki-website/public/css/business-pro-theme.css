/* Business Pro Theme Overrides */

/* Import base Space Dynamic styles */
@import url('templatemo-space-dynamic.css');

/* Business Pro Color Scheme - Blue/Gray */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --accent-color: #17a2b8;
    --text-color: #343a40;
    --bg-color: #f8f9fa;
}

/* Override main banner */
.main-banner {
    background: linear-gradient(135deg, #007bff 0%, #6c757d 100%) !important;
}

/* Business Pro specific styling */
body.template-business-pro {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.template-business-pro .main-banner h2 {
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.template-business-pro .main-banner p {
    color: #e3f2fd;
}

/* Header styling */
.template-business-pro .header-area {
    background: rgba(0, 123, 255, 0.95) !important;
    backdrop-filter: blur(10px);
}

.template-business-pro .header-area .nav-link {
    color: #ffffff !important;
    font-weight: 500;
}

.template-business-pro .header-area .nav-link:hover {
    color: #e3f2fd !important;
}

/* Button styling */
.template-business-pro .main-button a {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: 2px solid #007bff;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.template-business-pro .main-button a:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

/* Services section */
.template-business-pro .services .service-item {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.template-business-pro .services .service-item:hover {
    border-color: #007bff;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 123, 255, 0.15);
}

/* About section */
.template-business-pro .about-us {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Footer */
.template-business-pro .footer {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
}

/* Template indicator for Business Pro */
.template-business-pro .template-indicator {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

/* Professional card styling */
.template-business-pro .card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.template-business-pro .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

/* Professional typography */
.template-business-pro h1,
.template-business-pro h2,
.template-business-pro h3 {
    color: var(--text-color);
    font-weight: 600;
}

.template-business-pro .section-heading h2 {
    color: var(--primary-color);
    position: relative;
}

.template-business-pro .section-heading h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(45deg, #007bff, #17a2b8);
    border-radius: 2px;
}
