# Template Engine Implementation Documentation

## 🏗️ Architecture Overview

The AomProuddyogiki Template Engine is a sophisticated system that enables dynamic template switching with database-driven asset management. The system consists of multiple layers working together to provide seamless template functionality.

## 📁 Core Components

### 1. TemplateLoader Library (`app/Libraries/TemplateLoader.php`)

**Purpose**: Primary asset loading system with caching and fallback mechanisms.

**Key Methods**:
```php
// Get active template from database
public static function getActiveTemplate()

// Get CSS assets for active template
public static function getCssAssets()

// Get JS assets for active template  
public static function getJsAssets()

// Render CSS HTML tags
public static function renderCss()

// Render JS HTML tags
public static function renderJs()

// Clear cache after template switching
public static function clearCache()
```

**Features**:
- Static caching for performance
- Automatic fallback to Space Dynamic
- Database error handling
- Asset ordering by load_order field

### 2. Template Models

#### CmsTemplateModel (`app/Models/CmsTemplateModel.php`)
```php
// Get currently active template
public function getActiveTemplate()

// Set template as active (deactivates others)
public function setActiveTemplate($templateId)

// Get template by slug
public function getBySlug($slug)

// Install new template
public function installTemplate($templateData)
```

#### CmsTemplateAssetModel (`app/Models/CmsTemplateAssetModel.php`)
```php
// Get CSS assets for template
public function getCssAssets($templateId)

// Get JS assets for template
public function getJsAssets($templateId)

// Register new asset
public function registerAsset($templateId, $assetData)
```

#### CmsTemplateSettingModel (`app/Models/CmsTemplateSettingModel.php`)
```php
// Get all settings
public function getAllSettings()

// Set active template setting
public function setActiveTemplate($templateSlug)

// Get template engine configuration
public function getTemplateEngineConfig()
```

### 3. Template Services

#### TemplateEngineService (`app/Services/TemplateEngineService.php`)
```php
// Switch to different template
public function switchTemplate($templateSlug, $createBackup = false)

// Install template from TemplateMo
public function installTemplateFromTemplateM($templatemoId, $downloadUrl)

// Parse template structure
public function parseTemplateStructure($templateId, $extractPath)
```

#### FrontendTemplateService (`app/Services/FrontendTemplateService.php`)
```php
// Get active template with fallback
public function getActiveTemplate()

// Get CSS/JS assets with error handling
public function getActiveCssAssets()
public function getActiveJsAssets()

// Generate HTML for assets
public function getCssHtml()
public function getJsHtml()
```

## 🔄 Template Loading Flow

### 1. Frontend Request Flow
```
1. User visits http://localhost:8000/
2. Home controller loads
3. Main layout (layouts/main.php) renders
4. TemplateLoader::renderCss() called
5. Database queried for active template assets
6. CSS/JS HTML generated and inserted
7. Page renders with template assets
```

### 2. Template Switching Flow
```
1. Admin clicks "Activate" button
2. Switch modal opens with confirmation
3. Form submits to CmsTemplates::switch()
4. CmsTemplateModel::setActiveTemplate() called
5. Database updated (old template deactivated, new activated)
6. TemplateLoader::clearCache() called
7. Frontend immediately reflects new template
```

## 🎨 Template Structure

### Directory Organization
```
public/themes/
├── space-dynamic/          # Default template
│   ├── css/
│   │   ├── fontawesome.css
│   │   ├── templatemo-space-dynamic.css
│   │   ├── animated.css
│   │   └── owl.css
│   ├── js/
│   │   ├── owl-carousel.js
│   │   ├── animation.js
│   │   ├── imagesloaded.js
│   │   └── templatemo-custom.js
│   └── images/
│       └── [template images]
└── templatemo-558/         # Test template
    ├── css/
    │   └── style.css
    ├── js/
    │   └── custom.js
    └── images/
        └── [template images]
```

### Asset Registration
Templates must register their assets in the database:

```sql
INSERT INTO cms_template_assets (template_id, asset_type, file_name, file_path, is_critical, load_order) 
VALUES 
(1, 'css', 'fontawesome.css', 'themes/space-dynamic/css/fontawesome.css', 1, 1),
(1, 'css', 'templatemo-space-dynamic.css', 'themes/space-dynamic/css/templatemo-space-dynamic.css', 1, 2),
(1, 'js', 'templatemo-custom.js', 'themes/space-dynamic/js/templatemo-custom.js', 1, 4);
```

## 🔧 Implementation Details

### Main Layout Integration (`app/Views/layouts/main.php`)

**CSS Loading**:
```php
<!-- Template CSS Files (Dynamic Loading) -->
<?= \App\Libraries\TemplateLoader::renderCss() ?>
```

**JavaScript Loading**:
```php
<!-- Template JavaScript Files (Dynamic Loading) -->
<?= \App\Libraries\TemplateLoader::renderJs() ?>
```

### Error Handling Strategy

1. **Database Connection Failure**: Falls back to hardcoded Space Dynamic assets
2. **Missing Template**: Automatically uses Space Dynamic as default
3. **Missing Assets**: Skips missing files, continues with available assets
4. **Cache Issues**: Automatic cache clearing and regeneration

### Performance Optimizations

1. **Static Caching**: Template data cached in memory during request
2. **Asset Ordering**: CSS/JS loaded in optimal order for dependencies
3. **Critical Assets**: Priority loading for essential files
4. **Lazy Loading**: Non-critical assets can be loaded asynchronously

## 🎯 Template Development Guidelines

### Creating New Templates

1. **Directory Structure**: Follow themes/template-name/ pattern
2. **Asset Registration**: Register all CSS/JS files in database
3. **Load Order**: Set appropriate load_order for dependencies
4. **Critical Assets**: Mark essential files as critical
5. **Fallback Compatibility**: Ensure graceful degradation

### Template Metadata
```php
$templateData = [
    'name' => 'Template Name',
    'slug' => 'template-slug',
    'description' => 'Template description',
    'version' => '1.0.0',
    'author' => 'Author Name',
    'folder_path' => 'themes/template-slug',
    'status' => 'inactive',
    'config_data' => json_encode([
        'source' => 'custom',
        'features' => ['responsive', 'animated']
    ])
];
```

## 🔍 Debugging and Troubleshooting

### Debug Endpoints

1. **Template Debug**: `/admin/templates/debug`
   - Shows active template information
   - Lists all registered assets
   - Displays template engine configuration

2. **Quick Switch**: `/admin/templates/quick-switch/{id}`
   - Instantly switches to template by ID
   - Returns JSON response with status

### Common Issues

1. **Assets Not Loading**: Check file paths and database registration
2. **Template Not Switching**: Verify database constraints and cache clearing
3. **Missing Styles**: Ensure CSS files exist and are properly registered
4. **JavaScript Errors**: Check JS file loading order and dependencies

### Diagnostic Queries
```sql
-- Check active template
SELECT * FROM cms_templates WHERE is_default = 1;

-- Check template assets
SELECT ta.*, t.name as template_name 
FROM cms_template_assets ta 
JOIN cms_templates t ON ta.template_id = t.id 
WHERE t.is_default = 1 
ORDER BY ta.asset_type, ta.load_order;

-- Check for multiple active templates
SELECT COUNT(*) as active_count FROM cms_templates WHERE is_default = 1;
```

## 🚀 Future Enhancements

1. **Template Marketplace**: Integration with template repositories
2. **Visual Editor**: Drag-and-drop template customization
3. **Theme Inheritance**: Parent-child template relationships
4. **Asset Optimization**: Automatic minification and compression
5. **CDN Integration**: External asset hosting support
6. **Version Control**: Template versioning and rollback capabilities

This implementation provides a robust foundation for dynamic template management while maintaining performance and reliability.
