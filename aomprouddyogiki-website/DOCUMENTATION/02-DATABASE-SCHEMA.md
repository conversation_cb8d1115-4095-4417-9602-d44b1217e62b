# Database Schema Documentation

## 🗄️ Database Configuration

**Database Name**: `aomprouddyogiki_db`
**Host**: `localhost`
**User**: `root`
**Password**: (empty)
**Port**: `3306`

## 📊 Table Structures

### 1. cms_templates

Primary table for template metadata and configuration.

```sql
CREATE TABLE `cms_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL UNIQUE,
  `description` text,
  `version` varchar(50) DEFAULT '1.0.0',
  `author` varchar(255) DEFAULT NULL,
  `templatemo_id` varchar(50) DEFAULT NULL,
  `source_url` text,
  `preview_image` varchar(500) DEFAULT NULL,
  `folder_path` varchar(500) NOT NULL,
  `status` enum('active','inactive','installing','error') DEFAULT 'inactive',
  `is_default` tinyint(1) DEFAULT 0,
  `config_data` longtext,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
);
```

**Current Data**:
```
+----+----------------+----------------+----------+------------+
| id | name           | slug           | status   | is_default |
+----+----------------+----------------+----------+------------+
|  1 | Space Dynamic  | space-dynamic  | active   |          1 |
|  2 | TemplateMo 558 | templatemo-558 | inactive |          0 |
|  3 | TemplateMo 563 | templatemo-563 | inactive |          0 |
+----+----------------+----------------+----------+------------+
```

### 2. cms_template_assets

Asset tracking for CSS, JavaScript, and other files.

```sql
CREATE TABLE `cms_template_assets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL,
  `asset_type` enum('css','js','image','font','other') NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `is_critical` tinyint(1) DEFAULT 0,
  `load_order` int(11) DEFAULT 999,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `template_id` (`template_id`),
  KEY `asset_type` (`asset_type`),
  KEY `load_order` (`load_order`),
  CONSTRAINT `cms_template_assets_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `cms_templates` (`id`) ON DELETE CASCADE
);
```

**Sample Data for Space Dynamic (Template ID: 1)**:
```
CSS Assets:
- themes/space-dynamic/css/fontawesome.css (Critical, Order: 1)
- themes/space-dynamic/css/templatemo-space-dynamic.css (Critical, Order: 2)
- themes/space-dynamic/css/animated.css (Non-critical, Order: 3)
- themes/space-dynamic/css/owl.css (Non-critical, Order: 4)

JS Assets:
- themes/space-dynamic/js/owl-carousel.js (Order: 1)
- themes/space-dynamic/js/animation.js (Order: 2)
- themes/space-dynamic/js/imagesloaded.js (Order: 3)
- themes/space-dynamic/js/templatemo-custom.js (Critical, Order: 4)
```

### 3. cms_template_sections

Template component organization and mapping.

```sql
CREATE TABLE `cms_template_sections` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL,
  `section_name` varchar(255) NOT NULL,
  `section_type` varchar(100) DEFAULT NULL,
  `file_path` varchar(500) DEFAULT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `sort_order` int(11) DEFAULT 999,
  `config_data` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `template_id` (`template_id`),
  CONSTRAINT `cms_template_sections_ibfk_1` FOREIGN KEY (`template_id`) REFERENCES `cms_templates` (`id`) ON DELETE CASCADE
);
```

### 4. cms_template_settings

System configuration and template engine settings.

```sql
CREATE TABLE `cms_template_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(255) NOT NULL UNIQUE,
  `setting_value` longtext,
  `setting_type` enum('string','integer','boolean','json','text') DEFAULT 'string',
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
);
```

**Key Settings**:
```
- active_template: space-dynamic
- backup_enabled: true
- auto_parse_sections: true
- themes_directory: themes
- max_template_size: 50 (MB)
- allowed_extensions: zip, tar.gz
```

## 🔗 Database Relationships

### Foreign Key Constraints

1. **cms_template_assets.template_id** → **cms_templates.id**
   - CASCADE DELETE: Removing template deletes all assets
   - Ensures data integrity

2. **cms_template_sections.template_id** → **cms_templates.id**
   - CASCADE DELETE: Removing template deletes all sections
   - Maintains referential integrity

### Unique Constraints

1. **cms_templates.slug**: Ensures unique template identifiers
2. **cms_template_settings.setting_key**: Prevents duplicate settings

### Business Logic Constraints

1. **Single Active Template**: Only one template should have `is_default = 1`
2. **Asset Load Order**: CSS and JS assets loaded by `load_order` field
3. **Template Status**: Must be 'active' or 'inactive' for switching

## 📝 Database Queries

### Get Active Template
```sql
SELECT id, name, slug, status, is_default 
FROM cms_templates 
WHERE is_default = 1 
LIMIT 1;
```

### Get Template Assets
```sql
SELECT ta.file_path, ta.load_order, ta.is_critical
FROM cms_template_assets ta 
JOIN cms_templates t ON ta.template_id = t.id 
WHERE t.is_default = 1 AND ta.asset_type = 'css' 
ORDER BY ta.load_order;
```

### Switch Active Template
```sql
-- Deactivate all templates
UPDATE cms_templates SET is_default = 0;

-- Activate selected template
UPDATE cms_templates 
SET is_default = 1, status = 'active' 
WHERE id = ?;
```

## 🔧 Database Maintenance

### Backup Commands
```bash
# Full database backup
mysqldump -u root aomprouddyogiki_db > backup.sql

# Restore database
mysql -u root aomprouddyogiki_db < backup.sql
```

### Integrity Checks
```sql
-- Check for multiple active templates
SELECT COUNT(*) as active_count 
FROM cms_templates 
WHERE is_default = 1;

-- Check for orphaned assets
SELECT ta.* 
FROM cms_template_assets ta 
LEFT JOIN cms_templates t ON ta.template_id = t.id 
WHERE t.id IS NULL;
```

## 🚨 Known Database Issues

1. **Multiple Active Templates**: Previously had multiple templates with `is_default = 1`
2. **Asset Path Inconsistency**: Some assets point to old paths vs themes/ directory
3. **Missing Constraints**: No database-level constraint to enforce single active template

## 🎯 Database Optimization Recommendations

1. **Add Database Trigger**: Ensure only one template is default
2. **Index Optimization**: Add indexes for frequently queried fields
3. **Data Validation**: Add CHECK constraints for enum values
4. **Cleanup Procedures**: Regular maintenance for orphaned records
