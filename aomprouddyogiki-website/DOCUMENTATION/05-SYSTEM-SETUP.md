# System Setup and Configuration Documentation

## 🖥️ System Requirements

### Server Environment
- **Operating System**: Linux/Windows/macOS
- **Web Server**: Apache 2.4+ with mod_rewrite enabled
- **PHP**: 7.4+ (recommended 8.0+)
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **Memory**: Minimum 512MB RAM
- **Storage**: Minimum 1GB free space

### PHP Extensions Required
```
- php-mysql (database connectivity)
- php-mbstring (string handling)
- php-curl (HTTP requests)
- php-zip (template uploads)
- php-gd (image processing)
- php-json (JSON handling)
- php-xml (XML parsing)
```

### Development Environment
- **XAMPP/LAMPP**: Recommended for local development
- **Composer**: For dependency management
- **Git**: For version control

## 🚀 Installation Instructions

### Step 1: Environment Setup

#### XAMPP/LAMPP Installation
```bash
# Download and install XAMPP
# Start Apache and MySQL services
sudo /opt/lampp/lampp start

# Verify services
http://localhost/        # Apache test
http://localhost/phpmyadmin/  # MySQL test
```

#### PHP Configuration
```ini
# php.ini settings
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 256M
```

### Step 2: Database Setup

#### Create Database
```sql
CREATE DATABASE aomprouddyogiki_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### Import Database Schema
```bash
mysql -u root -p aomprouddyogiki_db < database/schema.sql
```

#### Database Tables
```sql
-- Core tables (auto-created by migrations)
cms_templates
cms_template_assets  
cms_template_sections
cms_template_settings
```

### Step 3: Project Setup

#### Clone/Download Project
```bash
# If using Git
git clone [repository-url] aomprouddyogiki-website

# Or extract ZIP file to web directory
unzip aomprouddyogiki-website.zip
```

#### File Permissions
```bash
# Set proper permissions
chmod -R 755 aomprouddyogiki-website/
chmod -R 777 aomprouddyogiki-website/writable/
chmod -R 777 aomprouddyogiki-website/public/themes/
```

#### Environment Configuration
```bash
# Copy environment file
cp env .env

# Edit configuration
nano .env
```

### Step 4: CodeIgniter Configuration

#### Database Configuration (`app/Config/Database.php`)
```php
public $default = [
    'DSN'      => '',
    'hostname' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'aomprouddyogiki_db',
    'DBDriver' => 'MySQLi',
    'DBPrefix' => '',
    'pConnect' => false,
    'DBDebug'  => (ENVIRONMENT !== 'production'),
    'charset'  => 'utf8mb4',
    'DBCollat' => 'utf8mb4_unicode_ci',
];
```

#### Base URL Configuration (`app/Config/App.php`)
```php
public $baseURL = 'http://localhost:8000/';
```

#### Routes Configuration (`app/Config/Routes.php`)
```php
// Frontend routes
$routes->get('/', 'Home::index');
$routes->get('/about', 'Home::about');

// Admin routes
$routes->group('admin', function($routes) {
    $routes->get('/', 'Admin::index');
    $routes->get('login', 'Admin::login');
    $routes->post('login', 'Admin::login');
    $routes->get('logout', 'Admin::logout');
    
    // Template management
    $routes->get('templates', 'CmsTemplates::index');
    $routes->post('templates/switch', 'CmsTemplates::switch');
    $routes->get('templates/install', 'CmsTemplates::install');
    $routes->post('templates/install', 'CmsTemplates::install');
});
```

## 🗄️ Database Configuration

### Connection Settings
```php
// Database credentials
$hostname = 'localhost';
$username = 'root';
$password = '';
$database = 'aomprouddyogiki_db';
$port = 3306;
```

### Initial Data Setup
```sql
-- Insert default Space Dynamic template
INSERT INTO cms_templates (name, slug, description, folder_path, status, is_default) 
VALUES ('Space Dynamic', 'space-dynamic', 'Default TemplateMo Space Dynamic template', 'themes/space-dynamic', 'active', 1);

-- Insert template assets
INSERT INTO cms_template_assets (template_id, asset_type, file_name, file_path, is_critical, load_order) 
VALUES 
(1, 'css', 'fontawesome.css', 'css/fontawesome.css', 1, 1),
(1, 'css', 'templatemo-space-dynamic.css', 'css/templatemo-space-dynamic.css', 1, 2),
(1, 'css', 'animated.css', 'css/animated.css', 0, 3),
(1, 'css', 'owl.css', 'css/owl.css', 0, 4),
(1, 'js', 'owl-carousel.js', 'js/owl-carousel.js', 0, 1),
(1, 'js', 'animation.js', 'js/animation.js', 0, 2),
(1, 'js', 'imagesloaded.js', 'js/imagesloaded.js', 0, 3),
(1, 'js', 'templatemo-custom.js', 'js/templatemo-custom.js', 1, 4);

-- Insert default settings
INSERT INTO cms_template_settings (setting_key, setting_value, setting_type) 
VALUES 
('active_template', 'space-dynamic', 'string'),
('backup_enabled', 'true', 'boolean'),
('auto_parse_sections', 'true', 'boolean'),
('themes_directory', 'themes', 'string');
```

## 🔧 Configuration Files

### Environment File (`.env`)
```ini
#--------------------------------------------------------------------
# ENVIRONMENT
#--------------------------------------------------------------------
CI_ENVIRONMENT = development

#--------------------------------------------------------------------
# APP
#--------------------------------------------------------------------
app.baseURL = 'http://localhost:8000/'
app.indexPage = ''

#--------------------------------------------------------------------
# DATABASE
#--------------------------------------------------------------------
database.default.hostname = localhost
database.default.database = aomprouddyogiki_db
database.default.username = root
database.default.password = 
database.default.DBDriver = MySQLi
```

### Apache Virtual Host (Optional)
```apache
<VirtualHost *:80>
    ServerName aomprouddyogiki.local
    DocumentRoot /opt/lampp/htdocs/aomF/aomprouddyogiki-website/public
    
    <Directory /opt/lampp/htdocs/aomF/aomprouddyogiki-website/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/aomprouddyogiki_error.log
    CustomLog ${APACHE_LOG_DIR}/aomprouddyogiki_access.log combined
</VirtualHost>
```

## 🎯 Access Information

### Frontend URLs
- **Homepage**: `http://localhost:8000/`
- **About Page**: `http://localhost:8000/about`
- **Services**: `http://localhost:8000/services/`
- **Contact**: `http://localhost:8000/contact`

### Admin Panel URLs
- **Admin Login**: `http://localhost:8000/admin/login`
- **Admin Dashboard**: `http://localhost:8000/admin/`
- **Template Management**: `http://localhost:8000/admin/templates`
- **Template Settings**: `http://localhost:8000/admin/templates/settings`

### Admin Credentials
```
Username: admin
Password: admin123
```

## 🔍 Verification Steps

### 1. Database Connection Test
```php
// Test database connectivity
$db = \Config\Database::connect();
if ($db->connID) {
    echo "Database connected successfully";
} else {
    echo "Database connection failed";
}
```

### 2. File Permissions Check
```bash
# Check writable directories
ls -la writable/
ls -la public/themes/
```

### 3. URL Rewriting Test
```
# Test clean URLs
http://localhost:8000/about  # Should work without index.php
```

### 4. Template System Test
```
# Check template loading
http://localhost:8000/admin/templates/debug
```

## 🚨 Common Setup Issues

### Issue 1: Database Connection Failed
**Solution**: Check credentials, ensure MySQL is running

### Issue 2: 404 Errors on Routes
**Solution**: Enable mod_rewrite, check .htaccess file

### Issue 3: Permission Denied Errors
**Solution**: Set proper file permissions (755/777)

### Issue 4: Template Assets Not Loading
**Solution**: Verify file paths and database asset registration

## 📞 Support

For setup assistance:
- **Project**: AomProuddyogiki CMS
- **Contact**: +91 6260383630
- **Location**: Indore, India
