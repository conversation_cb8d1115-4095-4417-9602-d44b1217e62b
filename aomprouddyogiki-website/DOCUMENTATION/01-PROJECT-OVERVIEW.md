# AomProuddyogiki CMS Template Engine - Project Overview

## 🎯 Project Description

The AomProuddyogiki CMS Template Engine is a comprehensive CodeIgniter 4-based content management system with advanced template switching capabilities. The project combines a professional business website with a powerful admin panel for dynamic template management.

## 🏗️ System Architecture

### Core Components

1. **Frontend Website** (`http://localhost:8000/`)
   - Professional business website for AomProuddyogiki Pvt. Ltd.
   - Services: Web Development, Mobile Development, Linux Hosting, Exam Preparation
   - Built with Space Dynamic template (TemplateMo tm-562)
   - Responsive design with animations and interactions

2. **Admin Panel** (`http://localhost:8000/admin/`)
   - Template management interface
   - Template installation from TemplateMo.com
   - Template switching with visual confirmation
   - Settings and configuration management

3. **Template Engine System**
   - Dynamic template loading from database
   - Asset management (CSS, JS, images)
   - Template switching with backup capabilities
   - Multi-template support with fallback mechanisms

## 🗄️ Database Structure

### Core Tables

1. **cms_templates**
   - Template metadata and configuration
   - Active template tracking (`is_default` flag)
   - Template status management

2. **cms_template_assets**
   - CSS and JavaScript file tracking
   - Load order and criticality management
   - Asset path and metadata storage

3. **cms_template_sections**
   - Template component organization
   - Section mapping and configuration

4. **cms_template_settings**
   - System configuration and preferences
   - Template engine settings

## 🔧 Technology Stack

- **Backend**: CodeIgniter 4 (PHP Framework)
- **Database**: MySQL
- **Frontend**: HTML5, CSS3, JavaScript
- **Template System**: Custom dynamic loading system
- **Admin Interface**: Bootstrap-based responsive design
- **Asset Management**: Database-driven with fallback mechanisms

## 📁 Directory Structure

```
aomprouddyogiki-website/
├── app/
│   ├── Controllers/
│   │   ├── Home.php (Frontend controller)
│   │   ├── CmsTemplates.php (Template management)
│   │   └── Admin.php (Admin authentication)
│   ├── Models/
│   │   ├── CmsTemplateModel.php
│   │   ├── CmsTemplateAssetModel.php
│   │   └── CmsTemplateSettingModel.php
│   ├── Views/
│   │   ├── layouts/main.php (Main layout with dynamic loading)
│   │   ├── home/ (Frontend pages)
│   │   └── admin/ (Admin interface)
│   ├── Services/
│   │   ├── TemplateEngineService.php
│   │   └── FrontendTemplateService.php
│   └── Libraries/
│       └── TemplateLoader.php (Current asset loading system)
├── public/
│   ├── themes/ (Template directories)
│   │   ├── space-dynamic/
│   │   └── templatemo-558/
│   ├── css/ (Original assets)
│   ├── js/ (Original assets)
│   └── images/ (Website images)
└── DOCUMENTATION/ (This documentation)
```

## 🎨 Template System Features

### Current Templates

1. **Space Dynamic** (Default)
   - Professional blue/white design
   - Complete asset set (CSS, JS, images)
   - Production-ready and fully functional

2. **TemplateMo 558** (Test Template)
   - Blue gradient styling
   - Visual indicator for testing
   - Custom CSS and JS for differentiation

### Template Management Features

- **Dynamic Asset Loading**: CSS/JS loaded from database
- **Template Switching**: Instant activation via admin panel
- **Backup System**: Optional backup before switching
- **Asset Validation**: File existence checking with fallbacks
- **Cache Management**: Template data caching with invalidation

## 🔐 Admin Panel Features

### Authentication
- Username: `admin`
- Password: `admin123`
- Session-based authentication

### Template Management
- Template gallery with visual previews
- Installation from TemplateMo.com URLs
- ZIP file upload for custom templates
- Template activation with confirmation modals
- Settings and configuration management

## 🌐 Frontend Features

### Website Sections
- **Hero Banner**: Company introduction and call-to-action
- **About/Services**: Service overview with icons
- **Our Services**: Detailed service descriptions with progress bars
- **Portfolio**: Service showcase with hover effects
- **Contact**: Contact form and company information

### Technical Features
- **Responsive Design**: Mobile-friendly layout
- **Animations**: WOW.js animations and smooth transitions
- **Interactive Elements**: Forms, buttons, and navigation
- **SEO Optimized**: Meta tags and structured content

## 🔄 Current System Status

### Working Components
✅ Database structure and relationships
✅ Admin panel authentication and interface
✅ Template management and switching logic
✅ Asset tracking and database storage
✅ Template installation and registration

### Known Issues
❌ **Frontend Loading Problem**: Website shows only loading animation
❌ **Asset Loading**: Dynamic template assets not rendering properly
❌ **Template Switching**: Frontend doesn't reflect template changes

## 🎯 Project Goals

1. **Resolve Frontend Loading**: Fix the persistent loading animation issue
2. **Complete Template Switching**: Ensure frontend reflects template changes
3. **Production Readiness**: Achieve stable, reliable system operation
4. **Scalability**: Support for unlimited template additions
5. **User Experience**: Seamless template management workflow

## 📞 Support Information

- **Project**: AomProuddyogiki Pvt. Ltd. Website with CMS
- **Location**: Indore, India
- **Contact**: +91 6260383630
- **Services**: Web Development, Mobile Apps, Linux Hosting, Exam Preparation

This documentation provides the foundation for understanding the current project state and continuing development work.
